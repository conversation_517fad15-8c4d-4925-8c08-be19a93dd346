{"name": "Khan Baba", "version": "1.0.0", "license": "0BSD", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "cd ios && xcodebuild -workspace KhanBaba.xcworkspace -scheme KhanBaba -configuration Release -destination generic/platform=iOS -archivePath KhanBaba.xcarchive archive"}, "dependencies": {"@likashefqet/react-native-image-zoom": "^4.3.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-picker/picker": "2.9.0", "@react-navigation/bottom-tabs": "^7.3.13", "@react-navigation/native": "^6.1.18", "@react-navigation/stack": "^7.2.7", "@reduxjs/toolkit": "^2.3.0", "@types/react": "18.2.41", "@types/react-dom": "18.2.17", "axios": "^1.9.0", "currency-symbol-map": "^5.1.0", "moment": "^2.30.1", "react": "18.3.1", "react-native": "0.76.8", "react-native-crypto": "^2.2.0", "react-native-device-info": "^14.0.0", "react-native-dropdown-picker": "^5.4.6", "react-native-gesture-handler": "^2.20.2", "react-native-haptic-feedback": "^2.3.3", "react-native-paper": "4.9.2", "react-native-picker-select": "^9.3.1", "react-native-push-notification": "^8.1.1", "react-native-reanimated": "^3.16.1", "react-native-reanimated-carousel": "^4.0.2", "react-native-safe-area-context": "4.12.0", "react-native-screens": "^4.4.0", "react-native-snap-carousel": "^3.9.1", "react-native-splash-screen": "^3.3.0", "react-native-swiper": "^1.6.0", "react-native-tab-view": "^4.0.10", "react-native-vector-icons": "^10.2.0", "react-native-webview": "13.12.5", "react-redux": "^9.2.0", "reanimated-bottom-sheet": "^1.0.0-alpha.22", "redux": "5.0.0", "uuid": "^11.0.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.2", "@babel/runtime": "^7.25.2", "@react-native-community/cli": "^18.0.0", "@react-native/babel-preset": "0.76.8", "@react-native/eslint-config": "0.76.8", "@react-native/metro-config": "0.76.8", "@react-native/typescript-config": "0.76.8", "@types/react": "~18.3.12", "@types/react-test-renderer": "^18.0.0", "@types/uuid": "^10.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.3.1", "typescript": "^5.3.3"}, "engines": {"node": ">=18"}, "private": true}