/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleObjCpp
 *
 * We create an umbrella header (and corresponding implementation) here since
 * Cxx compilation in BUCK has a limitation: source-code producing genrule()s
 * must have a single output. More files => more genrule()s => slower builds.
 */

#import "RNHapticFeedbackSpec.h"


@implementation NativeHapticFeedbackSpecBase


- (void)setEventEmitterCallback:(EventEmitterCallbackWrapper *)eventEmitterCallbackWrapper
{
  _eventEmitterCallback = std::move(eventEmitterCallbackWrapper->_eventEmitterCallback);
}
@end

@implementation RCTCxxConvert (NativeHapticFeedback_SpecTriggerOptions)
+ (RCTManagedPointer *)JS_NativeHapticFeedback_SpecTriggerOptions:(id)json
{
  return facebook::react::managedPointer<JS::NativeHapticFeedback::SpecTriggerOptions>(json);
}
@end
namespace facebook::react {
  
    static facebook::jsi::Value __hostFunction_NativeHapticFeedbackSpecJSI_trigger(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "trigger", @selector(trigger:options:), args, count);
    }

  NativeHapticFeedbackSpecJSI::NativeHapticFeedbackSpecJSI(const ObjCTurboModule::InitParams &params)
    : ObjCTurboModule(params) {
      
        methodMap_["trigger"] = MethodMetadata {2, __hostFunction_NativeHapticFeedbackSpecJSI_trigger};
        setMethodArgConversionSelector(@"trigger", 1, @"JS_NativeHapticFeedback_SpecTriggerOptions:");
  }
} // namespace facebook::react
