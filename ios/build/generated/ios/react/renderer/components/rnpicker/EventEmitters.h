
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateEventEmitterH.js
 */
#pragma once

#include <react/renderer/components/view/ViewEventEmitter.h>


namespace facebook::react {
class RNCAndroidDialogPickerEventEmitter : public ViewEventEmitter {
 public:
  using ViewEventEmitter::ViewEventEmitter;

  struct OnSelect {
      int position;
    };

  struct OnFocus {
      
    };

  struct OnBlur {
      
    };
  void onSelect(OnSelect value) const;

  void onFocus(OnFocus value) const;

  void onBlur(OnBlur value) const;
};
class RNCAndroidDropdownPickerEventEmitter : public ViewEventEmitter {
 public:
  using ViewEventEmitter::ViewEventEmitter;

  struct OnSelect {
      int position;
    };

  struct OnFocus {
      
    };

  struct OnBlur {
      
    };
  void onSelect(OnSelect value) const;

  void onFocus(OnFocus value) const;

  void onBlur(OnBlur value) const;
};
class RNCPickerEventEmitter : public ViewEventEmitter {
 public:
  using ViewEventEmitter::ViewEventEmitter;

  struct OnChange {
      std::string newValue;
    int newIndex;
    };
  void onChange(OnChange value) const;
};
} // namespace facebook::react
