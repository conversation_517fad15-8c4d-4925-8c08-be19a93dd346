
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateEventEmitterCpp.js
 */

#include <react/renderer/components/rnpicker/EventEmitters.h>


namespace facebook::react {

void RNCAndroidDialogPickerEventEmitter::onSelect(OnSelect $event) const {
  dispatchEvent("topSelect", [$event=std::move($event)](jsi::Runtime &runtime) {
    auto $payload = jsi::Object(runtime);
    $payload.setProperty(runtime, "position", $event.position);
    return $payload;
  });
}


void RNCAndroidDialogPickerEventEmitter::onFocus(OnFocus $event) const {
  dispatchEvent("topFocus", [](jsi::Runtime &runtime) {
    auto $payload = jsi::Object(runtime);
    
    return $payload;
  });
}


void RNCAndroidDialogPickerEventEmitter::onBlur(OnBlur $event) const {
  dispatchEvent("topBlur", [](jsi::Runtime &runtime) {
    auto $payload = jsi::Object(runtime);
    
    return $payload;
  });
}


void RNCAndroidDropdownPickerEventEmitter::onSelect(OnSelect $event) const {
  dispatchEvent("topSelect", [$event=std::move($event)](jsi::Runtime &runtime) {
    auto $payload = jsi::Object(runtime);
    $payload.setProperty(runtime, "position", $event.position);
    return $payload;
  });
}


void RNCAndroidDropdownPickerEventEmitter::onFocus(OnFocus $event) const {
  dispatchEvent("topFocus", [](jsi::Runtime &runtime) {
    auto $payload = jsi::Object(runtime);
    
    return $payload;
  });
}


void RNCAndroidDropdownPickerEventEmitter::onBlur(OnBlur $event) const {
  dispatchEvent("topBlur", [](jsi::Runtime &runtime) {
    auto $payload = jsi::Object(runtime);
    
    return $payload;
  });
}


void RNCPickerEventEmitter::onChange(OnChange $event) const {
  dispatchEvent("change", [$event=std::move($event)](jsi::Runtime &runtime) {
    auto $payload = jsi::Object(runtime);
    $payload.setProperty(runtime, "newValue", $event.newValue);
$payload.setProperty(runtime, "newIndex", $event.newIndex);
    return $payload;
  });
}

} // namespace facebook::react
