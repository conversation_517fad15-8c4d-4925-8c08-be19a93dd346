require_relative '../node_modules/react-native/scripts/react_native_pods'

platform :ios, '13.4'

target 'KhanBaba' do
  use_react_native!(
    :path => '../node_modules/react-native',
    :hermes_enabled => true
  )

  # Pods for KhanBaba
  pod 'RNVectorIcons', :path => '../node_modules/react-native-vector-icons'
  pod 'RNPushNotification', :path => '../node_modules/react-native-push-notification'
  pod 'RNDeviceInfo', :path => '../node_modules/react-native-device-info'
  pod 'react-native-splash-screen', :path => '../node_modules/react-native-splash-screen'

  target 'KhanBabaTests' do
    inherit! :complete
  end

  post_install do |installer|
    react_native_post_install(installer)
  end
end
