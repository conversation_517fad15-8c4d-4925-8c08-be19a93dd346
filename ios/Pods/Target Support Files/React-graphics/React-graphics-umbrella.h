#ifdef __OBJC__
#import <UIKit/UIKit.h>
#else
#ifndef FOUNDATION_EXPORT
#if defined(__cplusplus)
#define FOUNDATION_EXPORT extern "C"
#else
#define FOUNDATION_EXPORT extern
#endif
#endif
#endif

#import "react/renderer/graphics/BackgroundImage.h"
#import "react/renderer/graphics/BlendMode.h"
#import "react/renderer/graphics/BoxShadow.h"
#import "react/renderer/graphics/Color.h"
#import "react/renderer/graphics/ColorComponents.h"
#import "react/renderer/graphics/conversions.h"
#import "react/renderer/graphics/Filter.h"
#import "react/renderer/graphics/fromRawValueShared.h"
#import "react/renderer/graphics/Geometry.h"
#import "react/renderer/graphics/Isolation.h"
#import "react/renderer/graphics/Float.h"
#import "react/renderer/graphics/HostPlatformColor.h"
#import "react/renderer/graphics/PlatformColorParser.h"
#import "react/renderer/graphics/RCTPlatformColorUtils.h"
#import "react/renderer/graphics/Point.h"
#import "react/renderer/graphics/Rect.h"
#import "react/renderer/graphics/RectangleCorners.h"
#import "react/renderer/graphics/RectangleEdges.h"
#import "react/renderer/graphics/rounding.h"
#import "react/renderer/graphics/Size.h"
#import "react/renderer/graphics/Transform.h"
#import "react/renderer/graphics/ValueUnit.h"
#import "react/renderer/graphics/Vector.h"

FOUNDATION_EXPORT double react_renderer_graphicsVersionNumber;
FOUNDATION_EXPORT const unsigned char react_renderer_graphicsVersionString[];

