# /* **************************************************************************
#  *                                                                          *
#  *     (C) Copyright <PERSON> 2002.
#  *     Distributed under the Boost Software License, Version 1.0. (See
#  *     accompanying file LICENSE_1_0.txt or copy at
#  *     http://www.boost.org/LICENSE_1_0.txt)
#  *                                                                          *
#  ************************************************************************** */
#
# /* Revised by <PERSON> (2020) */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef BOOST_PREPROCESSOR_DETAIL_AUTO_REC_1024_HPP
# define BOOST_PREPROCESSOR_DETAIL_AUTO_REC_1024_HPP
#
# define BOOST_PP_NODE_ENTRY_1024(p) BOOST_PP_NODE_512(p)(p)(p)(p)(p)(p)(p)(p)(p)(p)
#
# define BOOST_PP_NODE_512(p) BOOST_PP_IIF(p(512), BOOST_PP_NODE_256, BOOST_PP_NODE_768)
#     define BOOST_PP_NODE_768(p) BOOST_PP_IIF(p(768), BOOST_PP_NODE_640, BOOST_PP_NODE_896)
#         define BOOST_PP_NODE_640(p) BOOST_PP_IIF(p(640), BOOST_PP_NODE_576, BOOST_PP_NODE_704)
#             define BOOST_PP_NODE_576(p) BOOST_PP_IIF(p(576), BOOST_PP_NODE_544, BOOST_PP_NODE_608)
#                 define BOOST_PP_NODE_544(p) BOOST_PP_IIF(p(544), BOOST_PP_NODE_528, BOOST_PP_NODE_560)
#                     define BOOST_PP_NODE_528(p) BOOST_PP_IIF(p(528), BOOST_PP_NODE_520, BOOST_PP_NODE_536)
#                         define BOOST_PP_NODE_520(p) BOOST_PP_IIF(p(520), BOOST_PP_NODE_516, BOOST_PP_NODE_524)
#                             define BOOST_PP_NODE_516(p) BOOST_PP_IIF(p(516), BOOST_PP_NODE_514, BOOST_PP_NODE_518)
#                                 define BOOST_PP_NODE_514(p) BOOST_PP_IIF(p(514), BOOST_PP_NODE_513, BOOST_PP_NODE_515)
#                                     define BOOST_PP_NODE_513(p) BOOST_PP_IIF(p(513), 513, 514)
#                                     define BOOST_PP_NODE_515(p) BOOST_PP_IIF(p(515), 515, 516)
#                                 define BOOST_PP_NODE_518(p) BOOST_PP_IIF(p(518), BOOST_PP_NODE_517, BOOST_PP_NODE_519)
#                                     define BOOST_PP_NODE_517(p) BOOST_PP_IIF(p(517), 517, 518)
#                                     define BOOST_PP_NODE_519(p) BOOST_PP_IIF(p(519), 519, 520)
#                             define BOOST_PP_NODE_524(p) BOOST_PP_IIF(p(524), BOOST_PP_NODE_522, BOOST_PP_NODE_526)
#                                 define BOOST_PP_NODE_522(p) BOOST_PP_IIF(p(522), BOOST_PP_NODE_521, BOOST_PP_NODE_523)
#                                     define BOOST_PP_NODE_521(p) BOOST_PP_IIF(p(521), 521, 522)
#                                     define BOOST_PP_NODE_523(p) BOOST_PP_IIF(p(523), 523, 524)
#                                 define BOOST_PP_NODE_526(p) BOOST_PP_IIF(p(526), BOOST_PP_NODE_525, BOOST_PP_NODE_527)
#                                     define BOOST_PP_NODE_525(p) BOOST_PP_IIF(p(525), 525, 526)
#                                     define BOOST_PP_NODE_527(p) BOOST_PP_IIF(p(527), 527, 528)
#                         define BOOST_PP_NODE_536(p) BOOST_PP_IIF(p(536), BOOST_PP_NODE_532, BOOST_PP_NODE_540)
#                             define BOOST_PP_NODE_532(p) BOOST_PP_IIF(p(532), BOOST_PP_NODE_530, BOOST_PP_NODE_534)
#                                 define BOOST_PP_NODE_530(p) BOOST_PP_IIF(p(530), BOOST_PP_NODE_529, BOOST_PP_NODE_531)
#                                     define BOOST_PP_NODE_529(p) BOOST_PP_IIF(p(529), 529, 530)
#                                     define BOOST_PP_NODE_531(p) BOOST_PP_IIF(p(531), 531, 532)
#                                 define BOOST_PP_NODE_534(p) BOOST_PP_IIF(p(534), BOOST_PP_NODE_533, BOOST_PP_NODE_535)
#                                     define BOOST_PP_NODE_533(p) BOOST_PP_IIF(p(533), 533, 534)
#                                     define BOOST_PP_NODE_535(p) BOOST_PP_IIF(p(535), 535, 536)
#                             define BOOST_PP_NODE_540(p) BOOST_PP_IIF(p(540), BOOST_PP_NODE_538, BOOST_PP_NODE_542)
#                                 define BOOST_PP_NODE_538(p) BOOST_PP_IIF(p(538), BOOST_PP_NODE_537, BOOST_PP_NODE_539)
#                                     define BOOST_PP_NODE_537(p) BOOST_PP_IIF(p(537), 537, 538)
#                                     define BOOST_PP_NODE_539(p) BOOST_PP_IIF(p(539), 539, 540)
#                                 define BOOST_PP_NODE_542(p) BOOST_PP_IIF(p(542), BOOST_PP_NODE_541, BOOST_PP_NODE_543)
#                                     define BOOST_PP_NODE_541(p) BOOST_PP_IIF(p(541), 541, 542)
#                                     define BOOST_PP_NODE_543(p) BOOST_PP_IIF(p(543), 543, 544)
#                     define BOOST_PP_NODE_560(p) BOOST_PP_IIF(p(560), BOOST_PP_NODE_552, BOOST_PP_NODE_568)
#                         define BOOST_PP_NODE_552(p) BOOST_PP_IIF(p(552), BOOST_PP_NODE_548, BOOST_PP_NODE_556)
#                             define BOOST_PP_NODE_548(p) BOOST_PP_IIF(p(548), BOOST_PP_NODE_546, BOOST_PP_NODE_550)
#                                 define BOOST_PP_NODE_546(p) BOOST_PP_IIF(p(546), BOOST_PP_NODE_545, BOOST_PP_NODE_547)
#                                     define BOOST_PP_NODE_545(p) BOOST_PP_IIF(p(545), 545, 546)
#                                     define BOOST_PP_NODE_547(p) BOOST_PP_IIF(p(547), 547, 548)
#                                 define BOOST_PP_NODE_550(p) BOOST_PP_IIF(p(550), BOOST_PP_NODE_549, BOOST_PP_NODE_551)
#                                     define BOOST_PP_NODE_549(p) BOOST_PP_IIF(p(549), 549, 550)
#                                     define BOOST_PP_NODE_551(p) BOOST_PP_IIF(p(551), 551, 552)
#                             define BOOST_PP_NODE_556(p) BOOST_PP_IIF(p(556), BOOST_PP_NODE_554, BOOST_PP_NODE_558)
#                                 define BOOST_PP_NODE_554(p) BOOST_PP_IIF(p(554), BOOST_PP_NODE_553, BOOST_PP_NODE_555)
#                                     define BOOST_PP_NODE_553(p) BOOST_PP_IIF(p(553), 553, 554)
#                                     define BOOST_PP_NODE_555(p) BOOST_PP_IIF(p(555), 555, 556)
#                                 define BOOST_PP_NODE_558(p) BOOST_PP_IIF(p(558), BOOST_PP_NODE_557, BOOST_PP_NODE_559)
#                                     define BOOST_PP_NODE_557(p) BOOST_PP_IIF(p(557), 557, 558)
#                                     define BOOST_PP_NODE_559(p) BOOST_PP_IIF(p(559), 559, 560)
#                         define BOOST_PP_NODE_568(p) BOOST_PP_IIF(p(568), BOOST_PP_NODE_564, BOOST_PP_NODE_572)
#                             define BOOST_PP_NODE_564(p) BOOST_PP_IIF(p(564), BOOST_PP_NODE_562, BOOST_PP_NODE_566)
#                                 define BOOST_PP_NODE_562(p) BOOST_PP_IIF(p(562), BOOST_PP_NODE_561, BOOST_PP_NODE_563)
#                                     define BOOST_PP_NODE_561(p) BOOST_PP_IIF(p(561), 561, 562)
#                                     define BOOST_PP_NODE_563(p) BOOST_PP_IIF(p(563), 563, 564)
#                                 define BOOST_PP_NODE_566(p) BOOST_PP_IIF(p(566), BOOST_PP_NODE_565, BOOST_PP_NODE_567)
#                                     define BOOST_PP_NODE_565(p) BOOST_PP_IIF(p(565), 565, 566)
#                                     define BOOST_PP_NODE_567(p) BOOST_PP_IIF(p(567), 567, 568)
#                             define BOOST_PP_NODE_572(p) BOOST_PP_IIF(p(572), BOOST_PP_NODE_570, BOOST_PP_NODE_574)
#                                 define BOOST_PP_NODE_570(p) BOOST_PP_IIF(p(570), BOOST_PP_NODE_569, BOOST_PP_NODE_571)
#                                     define BOOST_PP_NODE_569(p) BOOST_PP_IIF(p(569), 569, 570)
#                                     define BOOST_PP_NODE_571(p) BOOST_PP_IIF(p(571), 571, 572)
#                                 define BOOST_PP_NODE_574(p) BOOST_PP_IIF(p(574), BOOST_PP_NODE_573, BOOST_PP_NODE_575)
#                                     define BOOST_PP_NODE_573(p) BOOST_PP_IIF(p(573), 573, 574)
#                                     define BOOST_PP_NODE_575(p) BOOST_PP_IIF(p(575), 575, 576)
#                 define BOOST_PP_NODE_608(p) BOOST_PP_IIF(p(608), BOOST_PP_NODE_592, BOOST_PP_NODE_624)
#                     define BOOST_PP_NODE_592(p) BOOST_PP_IIF(p(592), BOOST_PP_NODE_584, BOOST_PP_NODE_600)
#                         define BOOST_PP_NODE_584(p) BOOST_PP_IIF(p(584), BOOST_PP_NODE_580, BOOST_PP_NODE_588)
#                             define BOOST_PP_NODE_580(p) BOOST_PP_IIF(p(580), BOOST_PP_NODE_578, BOOST_PP_NODE_582)
#                                 define BOOST_PP_NODE_578(p) BOOST_PP_IIF(p(578), BOOST_PP_NODE_577, BOOST_PP_NODE_579)
#                                     define BOOST_PP_NODE_577(p) BOOST_PP_IIF(p(577), 577, 578)
#                                     define BOOST_PP_NODE_579(p) BOOST_PP_IIF(p(579), 579, 580)
#                                 define BOOST_PP_NODE_582(p) BOOST_PP_IIF(p(582), BOOST_PP_NODE_581, BOOST_PP_NODE_583)
#                                     define BOOST_PP_NODE_581(p) BOOST_PP_IIF(p(581), 581, 582)
#                                     define BOOST_PP_NODE_583(p) BOOST_PP_IIF(p(583), 583, 584)
#                             define BOOST_PP_NODE_588(p) BOOST_PP_IIF(p(588), BOOST_PP_NODE_586, BOOST_PP_NODE_590)
#                                 define BOOST_PP_NODE_586(p) BOOST_PP_IIF(p(586), BOOST_PP_NODE_585, BOOST_PP_NODE_587)
#                                     define BOOST_PP_NODE_585(p) BOOST_PP_IIF(p(585), 585, 586)
#                                     define BOOST_PP_NODE_587(p) BOOST_PP_IIF(p(587), 587, 588)
#                                 define BOOST_PP_NODE_590(p) BOOST_PP_IIF(p(590), BOOST_PP_NODE_589, BOOST_PP_NODE_591)
#                                     define BOOST_PP_NODE_589(p) BOOST_PP_IIF(p(589), 589, 590)
#                                     define BOOST_PP_NODE_591(p) BOOST_PP_IIF(p(591), 591, 592)
#                         define BOOST_PP_NODE_600(p) BOOST_PP_IIF(p(600), BOOST_PP_NODE_596, BOOST_PP_NODE_604)
#                             define BOOST_PP_NODE_596(p) BOOST_PP_IIF(p(596), BOOST_PP_NODE_594, BOOST_PP_NODE_598)
#                                 define BOOST_PP_NODE_594(p) BOOST_PP_IIF(p(594), BOOST_PP_NODE_593, BOOST_PP_NODE_595)
#                                     define BOOST_PP_NODE_593(p) BOOST_PP_IIF(p(593), 593, 594)
#                                     define BOOST_PP_NODE_595(p) BOOST_PP_IIF(p(595), 595, 596)
#                                 define BOOST_PP_NODE_598(p) BOOST_PP_IIF(p(598), BOOST_PP_NODE_597, BOOST_PP_NODE_599)
#                                     define BOOST_PP_NODE_597(p) BOOST_PP_IIF(p(597), 597, 598)
#                                     define BOOST_PP_NODE_599(p) BOOST_PP_IIF(p(599), 599, 600)
#                             define BOOST_PP_NODE_604(p) BOOST_PP_IIF(p(604), BOOST_PP_NODE_602, BOOST_PP_NODE_606)
#                                 define BOOST_PP_NODE_602(p) BOOST_PP_IIF(p(602), BOOST_PP_NODE_601, BOOST_PP_NODE_603)
#                                     define BOOST_PP_NODE_601(p) BOOST_PP_IIF(p(601), 601, 602)
#                                     define BOOST_PP_NODE_603(p) BOOST_PP_IIF(p(603), 603, 604)
#                                 define BOOST_PP_NODE_606(p) BOOST_PP_IIF(p(606), BOOST_PP_NODE_605, BOOST_PP_NODE_607)
#                                     define BOOST_PP_NODE_605(p) BOOST_PP_IIF(p(605), 605, 606)
#                                     define BOOST_PP_NODE_607(p) BOOST_PP_IIF(p(607), 607, 608)
#                     define BOOST_PP_NODE_624(p) BOOST_PP_IIF(p(624), BOOST_PP_NODE_616, BOOST_PP_NODE_632)
#                         define BOOST_PP_NODE_616(p) BOOST_PP_IIF(p(616), BOOST_PP_NODE_612, BOOST_PP_NODE_620)
#                             define BOOST_PP_NODE_612(p) BOOST_PP_IIF(p(612), BOOST_PP_NODE_610, BOOST_PP_NODE_614)
#                                 define BOOST_PP_NODE_610(p) BOOST_PP_IIF(p(610), BOOST_PP_NODE_609, BOOST_PP_NODE_611)
#                                     define BOOST_PP_NODE_609(p) BOOST_PP_IIF(p(609), 609, 610)
#                                     define BOOST_PP_NODE_611(p) BOOST_PP_IIF(p(611), 611, 612)
#                                 define BOOST_PP_NODE_614(p) BOOST_PP_IIF(p(614), BOOST_PP_NODE_613, BOOST_PP_NODE_615)
#                                     define BOOST_PP_NODE_613(p) BOOST_PP_IIF(p(613), 613, 614)
#                                     define BOOST_PP_NODE_615(p) BOOST_PP_IIF(p(615), 615, 616)
#                             define BOOST_PP_NODE_620(p) BOOST_PP_IIF(p(620), BOOST_PP_NODE_618, BOOST_PP_NODE_622)
#                                 define BOOST_PP_NODE_618(p) BOOST_PP_IIF(p(618), BOOST_PP_NODE_617, BOOST_PP_NODE_619)
#                                     define BOOST_PP_NODE_617(p) BOOST_PP_IIF(p(617), 617, 618)
#                                     define BOOST_PP_NODE_619(p) BOOST_PP_IIF(p(619), 619, 620)
#                                 define BOOST_PP_NODE_622(p) BOOST_PP_IIF(p(622), BOOST_PP_NODE_621, BOOST_PP_NODE_623)
#                                     define BOOST_PP_NODE_621(p) BOOST_PP_IIF(p(621), 621, 622)
#                                     define BOOST_PP_NODE_623(p) BOOST_PP_IIF(p(623), 623, 624)
#                         define BOOST_PP_NODE_632(p) BOOST_PP_IIF(p(632), BOOST_PP_NODE_628, BOOST_PP_NODE_636)
#                             define BOOST_PP_NODE_628(p) BOOST_PP_IIF(p(628), BOOST_PP_NODE_626, BOOST_PP_NODE_630)
#                                 define BOOST_PP_NODE_626(p) BOOST_PP_IIF(p(626), BOOST_PP_NODE_625, BOOST_PP_NODE_627)
#                                     define BOOST_PP_NODE_625(p) BOOST_PP_IIF(p(625), 625, 626)
#                                     define BOOST_PP_NODE_627(p) BOOST_PP_IIF(p(627), 627, 628)
#                                 define BOOST_PP_NODE_630(p) BOOST_PP_IIF(p(630), BOOST_PP_NODE_629, BOOST_PP_NODE_631)
#                                     define BOOST_PP_NODE_629(p) BOOST_PP_IIF(p(629), 629, 630)
#                                     define BOOST_PP_NODE_631(p) BOOST_PP_IIF(p(631), 631, 632)
#                             define BOOST_PP_NODE_636(p) BOOST_PP_IIF(p(636), BOOST_PP_NODE_634, BOOST_PP_NODE_638)
#                                 define BOOST_PP_NODE_634(p) BOOST_PP_IIF(p(634), BOOST_PP_NODE_633, BOOST_PP_NODE_635)
#                                     define BOOST_PP_NODE_633(p) BOOST_PP_IIF(p(633), 633, 634)
#                                     define BOOST_PP_NODE_635(p) BOOST_PP_IIF(p(635), 635, 636)
#                                 define BOOST_PP_NODE_638(p) BOOST_PP_IIF(p(638), BOOST_PP_NODE_637, BOOST_PP_NODE_639)
#                                     define BOOST_PP_NODE_637(p) BOOST_PP_IIF(p(637), 637, 638)
#                                     define BOOST_PP_NODE_639(p) BOOST_PP_IIF(p(639), 639, 640)
#             define BOOST_PP_NODE_704(p) BOOST_PP_IIF(p(704), BOOST_PP_NODE_672, BOOST_PP_NODE_736)
#                 define BOOST_PP_NODE_672(p) BOOST_PP_IIF(p(672), BOOST_PP_NODE_656, BOOST_PP_NODE_688)
#                     define BOOST_PP_NODE_656(p) BOOST_PP_IIF(p(656), BOOST_PP_NODE_648, BOOST_PP_NODE_664)
#                         define BOOST_PP_NODE_648(p) BOOST_PP_IIF(p(648), BOOST_PP_NODE_644, BOOST_PP_NODE_652)
#                             define BOOST_PP_NODE_644(p) BOOST_PP_IIF(p(644), BOOST_PP_NODE_642, BOOST_PP_NODE_646)
#                                 define BOOST_PP_NODE_642(p) BOOST_PP_IIF(p(642), BOOST_PP_NODE_641, BOOST_PP_NODE_643)
#                                     define BOOST_PP_NODE_641(p) BOOST_PP_IIF(p(641), 641, 642)
#                                     define BOOST_PP_NODE_643(p) BOOST_PP_IIF(p(643), 643, 644)
#                                 define BOOST_PP_NODE_646(p) BOOST_PP_IIF(p(646), BOOST_PP_NODE_645, BOOST_PP_NODE_647)
#                                     define BOOST_PP_NODE_645(p) BOOST_PP_IIF(p(645), 645, 646)
#                                     define BOOST_PP_NODE_647(p) BOOST_PP_IIF(p(647), 647, 648)
#                             define BOOST_PP_NODE_652(p) BOOST_PP_IIF(p(652), BOOST_PP_NODE_650, BOOST_PP_NODE_654)
#                                 define BOOST_PP_NODE_650(p) BOOST_PP_IIF(p(650), BOOST_PP_NODE_649, BOOST_PP_NODE_651)
#                                     define BOOST_PP_NODE_649(p) BOOST_PP_IIF(p(649), 649, 650)
#                                     define BOOST_PP_NODE_651(p) BOOST_PP_IIF(p(651), 651, 652)
#                                 define BOOST_PP_NODE_654(p) BOOST_PP_IIF(p(654), BOOST_PP_NODE_653, BOOST_PP_NODE_655)
#                                     define BOOST_PP_NODE_653(p) BOOST_PP_IIF(p(653), 653, 654)
#                                     define BOOST_PP_NODE_655(p) BOOST_PP_IIF(p(655), 655, 656)
#                         define BOOST_PP_NODE_664(p) BOOST_PP_IIF(p(664), BOOST_PP_NODE_660, BOOST_PP_NODE_668)
#                             define BOOST_PP_NODE_660(p) BOOST_PP_IIF(p(660), BOOST_PP_NODE_658, BOOST_PP_NODE_662)
#                                 define BOOST_PP_NODE_658(p) BOOST_PP_IIF(p(658), BOOST_PP_NODE_657, BOOST_PP_NODE_659)
#                                     define BOOST_PP_NODE_657(p) BOOST_PP_IIF(p(657), 657, 658)
#                                     define BOOST_PP_NODE_659(p) BOOST_PP_IIF(p(659), 659, 660)
#                                 define BOOST_PP_NODE_662(p) BOOST_PP_IIF(p(662), BOOST_PP_NODE_661, BOOST_PP_NODE_663)
#                                     define BOOST_PP_NODE_661(p) BOOST_PP_IIF(p(661), 661, 662)
#                                     define BOOST_PP_NODE_663(p) BOOST_PP_IIF(p(663), 663, 664)
#                             define BOOST_PP_NODE_668(p) BOOST_PP_IIF(p(668), BOOST_PP_NODE_666, BOOST_PP_NODE_670)
#                                 define BOOST_PP_NODE_666(p) BOOST_PP_IIF(p(666), BOOST_PP_NODE_665, BOOST_PP_NODE_667)
#                                     define BOOST_PP_NODE_665(p) BOOST_PP_IIF(p(665), 665, 666)
#                                     define BOOST_PP_NODE_667(p) BOOST_PP_IIF(p(667), 667, 668)
#                                 define BOOST_PP_NODE_670(p) BOOST_PP_IIF(p(670), BOOST_PP_NODE_669, BOOST_PP_NODE_671)
#                                     define BOOST_PP_NODE_669(p) BOOST_PP_IIF(p(669), 669, 670)
#                                     define BOOST_PP_NODE_671(p) BOOST_PP_IIF(p(671), 671, 672)
#                     define BOOST_PP_NODE_688(p) BOOST_PP_IIF(p(688), BOOST_PP_NODE_680, BOOST_PP_NODE_696)
#                         define BOOST_PP_NODE_680(p) BOOST_PP_IIF(p(680), BOOST_PP_NODE_676, BOOST_PP_NODE_684)
#                             define BOOST_PP_NODE_676(p) BOOST_PP_IIF(p(676), BOOST_PP_NODE_674, BOOST_PP_NODE_678)
#                                 define BOOST_PP_NODE_674(p) BOOST_PP_IIF(p(674), BOOST_PP_NODE_673, BOOST_PP_NODE_675)
#                                     define BOOST_PP_NODE_673(p) BOOST_PP_IIF(p(673), 673, 674)
#                                     define BOOST_PP_NODE_675(p) BOOST_PP_IIF(p(675), 675, 676)
#                                 define BOOST_PP_NODE_678(p) BOOST_PP_IIF(p(678), BOOST_PP_NODE_677, BOOST_PP_NODE_679)
#                                     define BOOST_PP_NODE_677(p) BOOST_PP_IIF(p(677), 677, 678)
#                                     define BOOST_PP_NODE_679(p) BOOST_PP_IIF(p(679), 679, 680)
#                             define BOOST_PP_NODE_684(p) BOOST_PP_IIF(p(684), BOOST_PP_NODE_682, BOOST_PP_NODE_686)
#                                 define BOOST_PP_NODE_682(p) BOOST_PP_IIF(p(682), BOOST_PP_NODE_681, BOOST_PP_NODE_683)
#                                     define BOOST_PP_NODE_681(p) BOOST_PP_IIF(p(681), 681, 682)
#                                     define BOOST_PP_NODE_683(p) BOOST_PP_IIF(p(683), 683, 684)
#                                 define BOOST_PP_NODE_686(p) BOOST_PP_IIF(p(686), BOOST_PP_NODE_685, BOOST_PP_NODE_687)
#                                     define BOOST_PP_NODE_685(p) BOOST_PP_IIF(p(685), 685, 686)
#                                     define BOOST_PP_NODE_687(p) BOOST_PP_IIF(p(687), 687, 688)
#                         define BOOST_PP_NODE_696(p) BOOST_PP_IIF(p(696), BOOST_PP_NODE_692, BOOST_PP_NODE_700)
#                             define BOOST_PP_NODE_692(p) BOOST_PP_IIF(p(692), BOOST_PP_NODE_690, BOOST_PP_NODE_694)
#                                 define BOOST_PP_NODE_690(p) BOOST_PP_IIF(p(690), BOOST_PP_NODE_689, BOOST_PP_NODE_691)
#                                     define BOOST_PP_NODE_689(p) BOOST_PP_IIF(p(689), 689, 690)
#                                     define BOOST_PP_NODE_691(p) BOOST_PP_IIF(p(691), 691, 692)
#                                 define BOOST_PP_NODE_694(p) BOOST_PP_IIF(p(694), BOOST_PP_NODE_693, BOOST_PP_NODE_695)
#                                     define BOOST_PP_NODE_693(p) BOOST_PP_IIF(p(693), 693, 694)
#                                     define BOOST_PP_NODE_695(p) BOOST_PP_IIF(p(695), 695, 696)
#                             define BOOST_PP_NODE_700(p) BOOST_PP_IIF(p(700), BOOST_PP_NODE_698, BOOST_PP_NODE_702)
#                                 define BOOST_PP_NODE_698(p) BOOST_PP_IIF(p(698), BOOST_PP_NODE_697, BOOST_PP_NODE_699)
#                                     define BOOST_PP_NODE_697(p) BOOST_PP_IIF(p(697), 697, 698)
#                                     define BOOST_PP_NODE_699(p) BOOST_PP_IIF(p(699), 699, 700)
#                                 define BOOST_PP_NODE_702(p) BOOST_PP_IIF(p(702), BOOST_PP_NODE_701, BOOST_PP_NODE_703)
#                                     define BOOST_PP_NODE_701(p) BOOST_PP_IIF(p(701), 701, 702)
#                                     define BOOST_PP_NODE_703(p) BOOST_PP_IIF(p(703), 703, 704)
#                 define BOOST_PP_NODE_736(p) BOOST_PP_IIF(p(736), BOOST_PP_NODE_720, BOOST_PP_NODE_752)
#                     define BOOST_PP_NODE_720(p) BOOST_PP_IIF(p(720), BOOST_PP_NODE_712, BOOST_PP_NODE_728)
#                         define BOOST_PP_NODE_712(p) BOOST_PP_IIF(p(712), BOOST_PP_NODE_708, BOOST_PP_NODE_716)
#                             define BOOST_PP_NODE_708(p) BOOST_PP_IIF(p(708), BOOST_PP_NODE_706, BOOST_PP_NODE_710)
#                                 define BOOST_PP_NODE_706(p) BOOST_PP_IIF(p(706), BOOST_PP_NODE_705, BOOST_PP_NODE_707)
#                                     define BOOST_PP_NODE_705(p) BOOST_PP_IIF(p(705), 705, 706)
#                                     define BOOST_PP_NODE_707(p) BOOST_PP_IIF(p(707), 707, 708)
#                                 define BOOST_PP_NODE_710(p) BOOST_PP_IIF(p(710), BOOST_PP_NODE_709, BOOST_PP_NODE_711)
#                                     define BOOST_PP_NODE_709(p) BOOST_PP_IIF(p(709), 709, 710)
#                                     define BOOST_PP_NODE_711(p) BOOST_PP_IIF(p(711), 711, 712)
#                             define BOOST_PP_NODE_716(p) BOOST_PP_IIF(p(716), BOOST_PP_NODE_714, BOOST_PP_NODE_718)
#                                 define BOOST_PP_NODE_714(p) BOOST_PP_IIF(p(714), BOOST_PP_NODE_713, BOOST_PP_NODE_715)
#                                     define BOOST_PP_NODE_713(p) BOOST_PP_IIF(p(713), 713, 714)
#                                     define BOOST_PP_NODE_715(p) BOOST_PP_IIF(p(715), 715, 716)
#                                 define BOOST_PP_NODE_718(p) BOOST_PP_IIF(p(718), BOOST_PP_NODE_717, BOOST_PP_NODE_719)
#                                     define BOOST_PP_NODE_717(p) BOOST_PP_IIF(p(717), 717, 718)
#                                     define BOOST_PP_NODE_719(p) BOOST_PP_IIF(p(719), 719, 720)
#                         define BOOST_PP_NODE_728(p) BOOST_PP_IIF(p(728), BOOST_PP_NODE_724, BOOST_PP_NODE_732)
#                             define BOOST_PP_NODE_724(p) BOOST_PP_IIF(p(724), BOOST_PP_NODE_722, BOOST_PP_NODE_726)
#                                 define BOOST_PP_NODE_722(p) BOOST_PP_IIF(p(722), BOOST_PP_NODE_721, BOOST_PP_NODE_723)
#                                     define BOOST_PP_NODE_721(p) BOOST_PP_IIF(p(721), 721, 722)
#                                     define BOOST_PP_NODE_723(p) BOOST_PP_IIF(p(723), 723, 724)
#                                 define BOOST_PP_NODE_726(p) BOOST_PP_IIF(p(726), BOOST_PP_NODE_725, BOOST_PP_NODE_727)
#                                     define BOOST_PP_NODE_725(p) BOOST_PP_IIF(p(725), 725, 726)
#                                     define BOOST_PP_NODE_727(p) BOOST_PP_IIF(p(727), 727, 728)
#                             define BOOST_PP_NODE_732(p) BOOST_PP_IIF(p(732), BOOST_PP_NODE_730, BOOST_PP_NODE_734)
#                                 define BOOST_PP_NODE_730(p) BOOST_PP_IIF(p(730), BOOST_PP_NODE_729, BOOST_PP_NODE_731)
#                                     define BOOST_PP_NODE_729(p) BOOST_PP_IIF(p(729), 729, 730)
#                                     define BOOST_PP_NODE_731(p) BOOST_PP_IIF(p(731), 731, 732)
#                                 define BOOST_PP_NODE_734(p) BOOST_PP_IIF(p(734), BOOST_PP_NODE_733, BOOST_PP_NODE_735)
#                                     define BOOST_PP_NODE_733(p) BOOST_PP_IIF(p(733), 733, 734)
#                                     define BOOST_PP_NODE_735(p) BOOST_PP_IIF(p(735), 735, 736)
#                     define BOOST_PP_NODE_752(p) BOOST_PP_IIF(p(752), BOOST_PP_NODE_744, BOOST_PP_NODE_760)
#                         define BOOST_PP_NODE_744(p) BOOST_PP_IIF(p(744), BOOST_PP_NODE_740, BOOST_PP_NODE_748)
#                             define BOOST_PP_NODE_740(p) BOOST_PP_IIF(p(740), BOOST_PP_NODE_738, BOOST_PP_NODE_742)
#                                 define BOOST_PP_NODE_738(p) BOOST_PP_IIF(p(738), BOOST_PP_NODE_737, BOOST_PP_NODE_739)
#                                     define BOOST_PP_NODE_737(p) BOOST_PP_IIF(p(737), 737, 738)
#                                     define BOOST_PP_NODE_739(p) BOOST_PP_IIF(p(739), 739, 740)
#                                 define BOOST_PP_NODE_742(p) BOOST_PP_IIF(p(742), BOOST_PP_NODE_741, BOOST_PP_NODE_743)
#                                     define BOOST_PP_NODE_741(p) BOOST_PP_IIF(p(741), 741, 742)
#                                     define BOOST_PP_NODE_743(p) BOOST_PP_IIF(p(743), 743, 744)
#                             define BOOST_PP_NODE_748(p) BOOST_PP_IIF(p(748), BOOST_PP_NODE_746, BOOST_PP_NODE_750)
#                                 define BOOST_PP_NODE_746(p) BOOST_PP_IIF(p(746), BOOST_PP_NODE_745, BOOST_PP_NODE_747)
#                                     define BOOST_PP_NODE_745(p) BOOST_PP_IIF(p(745), 745, 746)
#                                     define BOOST_PP_NODE_747(p) BOOST_PP_IIF(p(747), 747, 748)
#                                 define BOOST_PP_NODE_750(p) BOOST_PP_IIF(p(750), BOOST_PP_NODE_749, BOOST_PP_NODE_751)
#                                     define BOOST_PP_NODE_749(p) BOOST_PP_IIF(p(749), 749, 750)
#                                     define BOOST_PP_NODE_751(p) BOOST_PP_IIF(p(751), 751, 752)
#                         define BOOST_PP_NODE_760(p) BOOST_PP_IIF(p(760), BOOST_PP_NODE_756, BOOST_PP_NODE_764)
#                             define BOOST_PP_NODE_756(p) BOOST_PP_IIF(p(756), BOOST_PP_NODE_754, BOOST_PP_NODE_758)
#                                 define BOOST_PP_NODE_754(p) BOOST_PP_IIF(p(754), BOOST_PP_NODE_753, BOOST_PP_NODE_755)
#                                     define BOOST_PP_NODE_753(p) BOOST_PP_IIF(p(753), 753, 754)
#                                     define BOOST_PP_NODE_755(p) BOOST_PP_IIF(p(755), 755, 756)
#                                 define BOOST_PP_NODE_758(p) BOOST_PP_IIF(p(758), BOOST_PP_NODE_757, BOOST_PP_NODE_759)
#                                     define BOOST_PP_NODE_757(p) BOOST_PP_IIF(p(757), 757, 758)
#                                     define BOOST_PP_NODE_759(p) BOOST_PP_IIF(p(759), 759, 760)
#                             define BOOST_PP_NODE_764(p) BOOST_PP_IIF(p(764), BOOST_PP_NODE_762, BOOST_PP_NODE_766)
#                                 define BOOST_PP_NODE_762(p) BOOST_PP_IIF(p(762), BOOST_PP_NODE_761, BOOST_PP_NODE_763)
#                                     define BOOST_PP_NODE_761(p) BOOST_PP_IIF(p(761), 761, 762)
#                                     define BOOST_PP_NODE_763(p) BOOST_PP_IIF(p(763), 763, 764)
#                                 define BOOST_PP_NODE_766(p) BOOST_PP_IIF(p(766), BOOST_PP_NODE_765, BOOST_PP_NODE_767)
#                                     define BOOST_PP_NODE_765(p) BOOST_PP_IIF(p(765), 765, 766)
#                                     define BOOST_PP_NODE_767(p) BOOST_PP_IIF(p(767), 767, 768)
#         define BOOST_PP_NODE_896(p) BOOST_PP_IIF(p(896), BOOST_PP_NODE_832, BOOST_PP_NODE_960)
#             define BOOST_PP_NODE_832(p) BOOST_PP_IIF(p(832), BOOST_PP_NODE_800, BOOST_PP_NODE_864)
#                 define BOOST_PP_NODE_800(p) BOOST_PP_IIF(p(800), BOOST_PP_NODE_784, BOOST_PP_NODE_816)
#                     define BOOST_PP_NODE_784(p) BOOST_PP_IIF(p(784), BOOST_PP_NODE_776, BOOST_PP_NODE_792)
#                         define BOOST_PP_NODE_776(p) BOOST_PP_IIF(p(776), BOOST_PP_NODE_772, BOOST_PP_NODE_780)
#                             define BOOST_PP_NODE_772(p) BOOST_PP_IIF(p(772), BOOST_PP_NODE_770, BOOST_PP_NODE_774)
#                                 define BOOST_PP_NODE_770(p) BOOST_PP_IIF(p(770), BOOST_PP_NODE_769, BOOST_PP_NODE_771)
#                                     define BOOST_PP_NODE_769(p) BOOST_PP_IIF(p(769), 769, 770)
#                                     define BOOST_PP_NODE_771(p) BOOST_PP_IIF(p(771), 771, 772)
#                                 define BOOST_PP_NODE_774(p) BOOST_PP_IIF(p(774), BOOST_PP_NODE_773, BOOST_PP_NODE_775)
#                                     define BOOST_PP_NODE_773(p) BOOST_PP_IIF(p(773), 773, 774)
#                                     define BOOST_PP_NODE_775(p) BOOST_PP_IIF(p(775), 775, 776)
#                             define BOOST_PP_NODE_780(p) BOOST_PP_IIF(p(780), BOOST_PP_NODE_778, BOOST_PP_NODE_782)
#                                 define BOOST_PP_NODE_778(p) BOOST_PP_IIF(p(778), BOOST_PP_NODE_777, BOOST_PP_NODE_779)
#                                     define BOOST_PP_NODE_777(p) BOOST_PP_IIF(p(777), 777, 778)
#                                     define BOOST_PP_NODE_779(p) BOOST_PP_IIF(p(779), 779, 780)
#                                 define BOOST_PP_NODE_782(p) BOOST_PP_IIF(p(782), BOOST_PP_NODE_781, BOOST_PP_NODE_783)
#                                     define BOOST_PP_NODE_781(p) BOOST_PP_IIF(p(781), 781, 782)
#                                     define BOOST_PP_NODE_783(p) BOOST_PP_IIF(p(783), 783, 784)
#                         define BOOST_PP_NODE_792(p) BOOST_PP_IIF(p(792), BOOST_PP_NODE_788, BOOST_PP_NODE_796)
#                             define BOOST_PP_NODE_788(p) BOOST_PP_IIF(p(788), BOOST_PP_NODE_786, BOOST_PP_NODE_790)
#                                 define BOOST_PP_NODE_786(p) BOOST_PP_IIF(p(786), BOOST_PP_NODE_785, BOOST_PP_NODE_787)
#                                     define BOOST_PP_NODE_785(p) BOOST_PP_IIF(p(785), 785, 786)
#                                     define BOOST_PP_NODE_787(p) BOOST_PP_IIF(p(787), 787, 788)
#                                 define BOOST_PP_NODE_790(p) BOOST_PP_IIF(p(790), BOOST_PP_NODE_789, BOOST_PP_NODE_791)
#                                     define BOOST_PP_NODE_789(p) BOOST_PP_IIF(p(789), 789, 790)
#                                     define BOOST_PP_NODE_791(p) BOOST_PP_IIF(p(791), 791, 792)
#                             define BOOST_PP_NODE_796(p) BOOST_PP_IIF(p(796), BOOST_PP_NODE_794, BOOST_PP_NODE_798)
#                                 define BOOST_PP_NODE_794(p) BOOST_PP_IIF(p(794), BOOST_PP_NODE_793, BOOST_PP_NODE_795)
#                                     define BOOST_PP_NODE_793(p) BOOST_PP_IIF(p(793), 793, 794)
#                                     define BOOST_PP_NODE_795(p) BOOST_PP_IIF(p(795), 795, 796)
#                                 define BOOST_PP_NODE_798(p) BOOST_PP_IIF(p(798), BOOST_PP_NODE_797, BOOST_PP_NODE_799)
#                                     define BOOST_PP_NODE_797(p) BOOST_PP_IIF(p(797), 797, 798)
#                                     define BOOST_PP_NODE_799(p) BOOST_PP_IIF(p(799), 799, 800)
#                     define BOOST_PP_NODE_816(p) BOOST_PP_IIF(p(816), BOOST_PP_NODE_808, BOOST_PP_NODE_824)
#                         define BOOST_PP_NODE_808(p) BOOST_PP_IIF(p(808), BOOST_PP_NODE_804, BOOST_PP_NODE_812)
#                             define BOOST_PP_NODE_804(p) BOOST_PP_IIF(p(804), BOOST_PP_NODE_802, BOOST_PP_NODE_806)
#                                 define BOOST_PP_NODE_802(p) BOOST_PP_IIF(p(802), BOOST_PP_NODE_801, BOOST_PP_NODE_803)
#                                     define BOOST_PP_NODE_801(p) BOOST_PP_IIF(p(801), 801, 802)
#                                     define BOOST_PP_NODE_803(p) BOOST_PP_IIF(p(803), 803, 804)
#                                 define BOOST_PP_NODE_806(p) BOOST_PP_IIF(p(806), BOOST_PP_NODE_805, BOOST_PP_NODE_807)
#                                     define BOOST_PP_NODE_805(p) BOOST_PP_IIF(p(805), 805, 806)
#                                     define BOOST_PP_NODE_807(p) BOOST_PP_IIF(p(807), 807, 808)
#                             define BOOST_PP_NODE_812(p) BOOST_PP_IIF(p(812), BOOST_PP_NODE_810, BOOST_PP_NODE_814)
#                                 define BOOST_PP_NODE_810(p) BOOST_PP_IIF(p(810), BOOST_PP_NODE_809, BOOST_PP_NODE_811)
#                                     define BOOST_PP_NODE_809(p) BOOST_PP_IIF(p(809), 809, 810)
#                                     define BOOST_PP_NODE_811(p) BOOST_PP_IIF(p(811), 811, 812)
#                                 define BOOST_PP_NODE_814(p) BOOST_PP_IIF(p(814), BOOST_PP_NODE_813, BOOST_PP_NODE_815)
#                                     define BOOST_PP_NODE_813(p) BOOST_PP_IIF(p(813), 813, 814)
#                                     define BOOST_PP_NODE_815(p) BOOST_PP_IIF(p(815), 815, 816)
#                         define BOOST_PP_NODE_824(p) BOOST_PP_IIF(p(824), BOOST_PP_NODE_820, BOOST_PP_NODE_828)
#                             define BOOST_PP_NODE_820(p) BOOST_PP_IIF(p(820), BOOST_PP_NODE_818, BOOST_PP_NODE_822)
#                                 define BOOST_PP_NODE_818(p) BOOST_PP_IIF(p(818), BOOST_PP_NODE_817, BOOST_PP_NODE_819)
#                                     define BOOST_PP_NODE_817(p) BOOST_PP_IIF(p(817), 817, 818)
#                                     define BOOST_PP_NODE_819(p) BOOST_PP_IIF(p(819), 819, 820)
#                                 define BOOST_PP_NODE_822(p) BOOST_PP_IIF(p(822), BOOST_PP_NODE_821, BOOST_PP_NODE_823)
#                                     define BOOST_PP_NODE_821(p) BOOST_PP_IIF(p(821), 821, 822)
#                                     define BOOST_PP_NODE_823(p) BOOST_PP_IIF(p(823), 823, 824)
#                             define BOOST_PP_NODE_828(p) BOOST_PP_IIF(p(828), BOOST_PP_NODE_826, BOOST_PP_NODE_830)
#                                 define BOOST_PP_NODE_826(p) BOOST_PP_IIF(p(826), BOOST_PP_NODE_825, BOOST_PP_NODE_827)
#                                     define BOOST_PP_NODE_825(p) BOOST_PP_IIF(p(825), 825, 826)
#                                     define BOOST_PP_NODE_827(p) BOOST_PP_IIF(p(827), 827, 828)
#                                 define BOOST_PP_NODE_830(p) BOOST_PP_IIF(p(830), BOOST_PP_NODE_829, BOOST_PP_NODE_831)
#                                     define BOOST_PP_NODE_829(p) BOOST_PP_IIF(p(829), 829, 830)
#                                     define BOOST_PP_NODE_831(p) BOOST_PP_IIF(p(831), 831, 832)
#                 define BOOST_PP_NODE_864(p) BOOST_PP_IIF(p(864), BOOST_PP_NODE_848, BOOST_PP_NODE_880)
#                     define BOOST_PP_NODE_848(p) BOOST_PP_IIF(p(848), BOOST_PP_NODE_840, BOOST_PP_NODE_856)
#                         define BOOST_PP_NODE_840(p) BOOST_PP_IIF(p(840), BOOST_PP_NODE_836, BOOST_PP_NODE_844)
#                             define BOOST_PP_NODE_836(p) BOOST_PP_IIF(p(836), BOOST_PP_NODE_834, BOOST_PP_NODE_838)
#                                 define BOOST_PP_NODE_834(p) BOOST_PP_IIF(p(834), BOOST_PP_NODE_833, BOOST_PP_NODE_835)
#                                     define BOOST_PP_NODE_833(p) BOOST_PP_IIF(p(833), 833, 834)
#                                     define BOOST_PP_NODE_835(p) BOOST_PP_IIF(p(835), 835, 836)
#                                 define BOOST_PP_NODE_838(p) BOOST_PP_IIF(p(838), BOOST_PP_NODE_837, BOOST_PP_NODE_839)
#                                     define BOOST_PP_NODE_837(p) BOOST_PP_IIF(p(837), 837, 838)
#                                     define BOOST_PP_NODE_839(p) BOOST_PP_IIF(p(839), 839, 840)
#                             define BOOST_PP_NODE_844(p) BOOST_PP_IIF(p(844), BOOST_PP_NODE_842, BOOST_PP_NODE_846)
#                                 define BOOST_PP_NODE_842(p) BOOST_PP_IIF(p(842), BOOST_PP_NODE_841, BOOST_PP_NODE_843)
#                                     define BOOST_PP_NODE_841(p) BOOST_PP_IIF(p(841), 841, 842)
#                                     define BOOST_PP_NODE_843(p) BOOST_PP_IIF(p(843), 843, 844)
#                                 define BOOST_PP_NODE_846(p) BOOST_PP_IIF(p(846), BOOST_PP_NODE_845, BOOST_PP_NODE_847)
#                                     define BOOST_PP_NODE_845(p) BOOST_PP_IIF(p(845), 845, 846)
#                                     define BOOST_PP_NODE_847(p) BOOST_PP_IIF(p(847), 847, 848)
#                         define BOOST_PP_NODE_856(p) BOOST_PP_IIF(p(856), BOOST_PP_NODE_852, BOOST_PP_NODE_860)
#                             define BOOST_PP_NODE_852(p) BOOST_PP_IIF(p(852), BOOST_PP_NODE_850, BOOST_PP_NODE_854)
#                                 define BOOST_PP_NODE_850(p) BOOST_PP_IIF(p(850), BOOST_PP_NODE_849, BOOST_PP_NODE_851)
#                                     define BOOST_PP_NODE_849(p) BOOST_PP_IIF(p(849), 849, 850)
#                                     define BOOST_PP_NODE_851(p) BOOST_PP_IIF(p(851), 851, 852)
#                                 define BOOST_PP_NODE_854(p) BOOST_PP_IIF(p(854), BOOST_PP_NODE_853, BOOST_PP_NODE_855)
#                                     define BOOST_PP_NODE_853(p) BOOST_PP_IIF(p(853), 853, 854)
#                                     define BOOST_PP_NODE_855(p) BOOST_PP_IIF(p(855), 855, 856)
#                             define BOOST_PP_NODE_860(p) BOOST_PP_IIF(p(860), BOOST_PP_NODE_858, BOOST_PP_NODE_862)
#                                 define BOOST_PP_NODE_858(p) BOOST_PP_IIF(p(858), BOOST_PP_NODE_857, BOOST_PP_NODE_859)
#                                     define BOOST_PP_NODE_857(p) BOOST_PP_IIF(p(857), 857, 858)
#                                     define BOOST_PP_NODE_859(p) BOOST_PP_IIF(p(859), 859, 860)
#                                 define BOOST_PP_NODE_862(p) BOOST_PP_IIF(p(862), BOOST_PP_NODE_861, BOOST_PP_NODE_863)
#                                     define BOOST_PP_NODE_861(p) BOOST_PP_IIF(p(861), 861, 862)
#                                     define BOOST_PP_NODE_863(p) BOOST_PP_IIF(p(863), 863, 864)
#                     define BOOST_PP_NODE_880(p) BOOST_PP_IIF(p(880), BOOST_PP_NODE_872, BOOST_PP_NODE_888)
#                         define BOOST_PP_NODE_872(p) BOOST_PP_IIF(p(872), BOOST_PP_NODE_868, BOOST_PP_NODE_876)
#                             define BOOST_PP_NODE_868(p) BOOST_PP_IIF(p(868), BOOST_PP_NODE_866, BOOST_PP_NODE_870)
#                                 define BOOST_PP_NODE_866(p) BOOST_PP_IIF(p(866), BOOST_PP_NODE_865, BOOST_PP_NODE_867)
#                                     define BOOST_PP_NODE_865(p) BOOST_PP_IIF(p(865), 865, 866)
#                                     define BOOST_PP_NODE_867(p) BOOST_PP_IIF(p(867), 867, 868)
#                                 define BOOST_PP_NODE_870(p) BOOST_PP_IIF(p(870), BOOST_PP_NODE_869, BOOST_PP_NODE_871)
#                                     define BOOST_PP_NODE_869(p) BOOST_PP_IIF(p(869), 869, 870)
#                                     define BOOST_PP_NODE_871(p) BOOST_PP_IIF(p(871), 871, 872)
#                             define BOOST_PP_NODE_876(p) BOOST_PP_IIF(p(876), BOOST_PP_NODE_874, BOOST_PP_NODE_878)
#                                 define BOOST_PP_NODE_874(p) BOOST_PP_IIF(p(874), BOOST_PP_NODE_873, BOOST_PP_NODE_875)
#                                     define BOOST_PP_NODE_873(p) BOOST_PP_IIF(p(873), 873, 874)
#                                     define BOOST_PP_NODE_875(p) BOOST_PP_IIF(p(875), 875, 876)
#                                 define BOOST_PP_NODE_878(p) BOOST_PP_IIF(p(878), BOOST_PP_NODE_877, BOOST_PP_NODE_879)
#                                     define BOOST_PP_NODE_877(p) BOOST_PP_IIF(p(877), 877, 878)
#                                     define BOOST_PP_NODE_879(p) BOOST_PP_IIF(p(879), 879, 880)
#                         define BOOST_PP_NODE_888(p) BOOST_PP_IIF(p(888), BOOST_PP_NODE_884, BOOST_PP_NODE_892)
#                             define BOOST_PP_NODE_884(p) BOOST_PP_IIF(p(884), BOOST_PP_NODE_882, BOOST_PP_NODE_886)
#                                 define BOOST_PP_NODE_882(p) BOOST_PP_IIF(p(882), BOOST_PP_NODE_881, BOOST_PP_NODE_883)
#                                     define BOOST_PP_NODE_881(p) BOOST_PP_IIF(p(881), 881, 882)
#                                     define BOOST_PP_NODE_883(p) BOOST_PP_IIF(p(883), 883, 884)
#                                 define BOOST_PP_NODE_886(p) BOOST_PP_IIF(p(886), BOOST_PP_NODE_885, BOOST_PP_NODE_887)
#                                     define BOOST_PP_NODE_885(p) BOOST_PP_IIF(p(885), 885, 886)
#                                     define BOOST_PP_NODE_887(p) BOOST_PP_IIF(p(887), 887, 888)
#                             define BOOST_PP_NODE_892(p) BOOST_PP_IIF(p(892), BOOST_PP_NODE_890, BOOST_PP_NODE_894)
#                                 define BOOST_PP_NODE_890(p) BOOST_PP_IIF(p(890), BOOST_PP_NODE_889, BOOST_PP_NODE_891)
#                                     define BOOST_PP_NODE_889(p) BOOST_PP_IIF(p(889), 889, 890)
#                                     define BOOST_PP_NODE_891(p) BOOST_PP_IIF(p(891), 891, 892)
#                                 define BOOST_PP_NODE_894(p) BOOST_PP_IIF(p(894), BOOST_PP_NODE_893, BOOST_PP_NODE_895)
#                                     define BOOST_PP_NODE_893(p) BOOST_PP_IIF(p(893), 893, 894)
#                                     define BOOST_PP_NODE_895(p) BOOST_PP_IIF(p(895), 895, 896)
#             define BOOST_PP_NODE_960(p) BOOST_PP_IIF(p(960), BOOST_PP_NODE_928, BOOST_PP_NODE_992)
#                 define BOOST_PP_NODE_928(p) BOOST_PP_IIF(p(928), BOOST_PP_NODE_912, BOOST_PP_NODE_944)
#                     define BOOST_PP_NODE_912(p) BOOST_PP_IIF(p(912), BOOST_PP_NODE_904, BOOST_PP_NODE_920)
#                         define BOOST_PP_NODE_904(p) BOOST_PP_IIF(p(904), BOOST_PP_NODE_900, BOOST_PP_NODE_908)
#                             define BOOST_PP_NODE_900(p) BOOST_PP_IIF(p(900), BOOST_PP_NODE_898, BOOST_PP_NODE_902)
#                                 define BOOST_PP_NODE_898(p) BOOST_PP_IIF(p(898), BOOST_PP_NODE_897, BOOST_PP_NODE_899)
#                                     define BOOST_PP_NODE_897(p) BOOST_PP_IIF(p(897), 897, 898)
#                                     define BOOST_PP_NODE_899(p) BOOST_PP_IIF(p(899), 899, 900)
#                                 define BOOST_PP_NODE_902(p) BOOST_PP_IIF(p(902), BOOST_PP_NODE_901, BOOST_PP_NODE_903)
#                                     define BOOST_PP_NODE_901(p) BOOST_PP_IIF(p(901), 901, 902)
#                                     define BOOST_PP_NODE_903(p) BOOST_PP_IIF(p(903), 903, 904)
#                             define BOOST_PP_NODE_908(p) BOOST_PP_IIF(p(908), BOOST_PP_NODE_906, BOOST_PP_NODE_910)
#                                 define BOOST_PP_NODE_906(p) BOOST_PP_IIF(p(906), BOOST_PP_NODE_905, BOOST_PP_NODE_907)
#                                     define BOOST_PP_NODE_905(p) BOOST_PP_IIF(p(905), 905, 906)
#                                     define BOOST_PP_NODE_907(p) BOOST_PP_IIF(p(907), 907, 908)
#                                 define BOOST_PP_NODE_910(p) BOOST_PP_IIF(p(910), BOOST_PP_NODE_909, BOOST_PP_NODE_911)
#                                     define BOOST_PP_NODE_909(p) BOOST_PP_IIF(p(909), 909, 910)
#                                     define BOOST_PP_NODE_911(p) BOOST_PP_IIF(p(911), 911, 912)
#                         define BOOST_PP_NODE_920(p) BOOST_PP_IIF(p(920), BOOST_PP_NODE_916, BOOST_PP_NODE_924)
#                             define BOOST_PP_NODE_916(p) BOOST_PP_IIF(p(916), BOOST_PP_NODE_914, BOOST_PP_NODE_918)
#                                 define BOOST_PP_NODE_914(p) BOOST_PP_IIF(p(914), BOOST_PP_NODE_913, BOOST_PP_NODE_915)
#                                     define BOOST_PP_NODE_913(p) BOOST_PP_IIF(p(913), 913, 914)
#                                     define BOOST_PP_NODE_915(p) BOOST_PP_IIF(p(915), 915, 916)
#                                 define BOOST_PP_NODE_918(p) BOOST_PP_IIF(p(918), BOOST_PP_NODE_917, BOOST_PP_NODE_919)
#                                     define BOOST_PP_NODE_917(p) BOOST_PP_IIF(p(917), 917, 918)
#                                     define BOOST_PP_NODE_919(p) BOOST_PP_IIF(p(919), 919, 920)
#                             define BOOST_PP_NODE_924(p) BOOST_PP_IIF(p(924), BOOST_PP_NODE_922, BOOST_PP_NODE_926)
#                                 define BOOST_PP_NODE_922(p) BOOST_PP_IIF(p(922), BOOST_PP_NODE_921, BOOST_PP_NODE_923)
#                                     define BOOST_PP_NODE_921(p) BOOST_PP_IIF(p(921), 921, 922)
#                                     define BOOST_PP_NODE_923(p) BOOST_PP_IIF(p(923), 923, 924)
#                                 define BOOST_PP_NODE_926(p) BOOST_PP_IIF(p(926), BOOST_PP_NODE_925, BOOST_PP_NODE_927)
#                                     define BOOST_PP_NODE_925(p) BOOST_PP_IIF(p(925), 925, 926)
#                                     define BOOST_PP_NODE_927(p) BOOST_PP_IIF(p(927), 927, 928)
#                     define BOOST_PP_NODE_944(p) BOOST_PP_IIF(p(944), BOOST_PP_NODE_936, BOOST_PP_NODE_952)
#                         define BOOST_PP_NODE_936(p) BOOST_PP_IIF(p(936), BOOST_PP_NODE_932, BOOST_PP_NODE_940)
#                             define BOOST_PP_NODE_932(p) BOOST_PP_IIF(p(932), BOOST_PP_NODE_930, BOOST_PP_NODE_934)
#                                 define BOOST_PP_NODE_930(p) BOOST_PP_IIF(p(930), BOOST_PP_NODE_929, BOOST_PP_NODE_931)
#                                     define BOOST_PP_NODE_929(p) BOOST_PP_IIF(p(929), 929, 930)
#                                     define BOOST_PP_NODE_931(p) BOOST_PP_IIF(p(931), 931, 932)
#                                 define BOOST_PP_NODE_934(p) BOOST_PP_IIF(p(934), BOOST_PP_NODE_933, BOOST_PP_NODE_935)
#                                     define BOOST_PP_NODE_933(p) BOOST_PP_IIF(p(933), 933, 934)
#                                     define BOOST_PP_NODE_935(p) BOOST_PP_IIF(p(935), 935, 936)
#                             define BOOST_PP_NODE_940(p) BOOST_PP_IIF(p(940), BOOST_PP_NODE_938, BOOST_PP_NODE_942)
#                                 define BOOST_PP_NODE_938(p) BOOST_PP_IIF(p(938), BOOST_PP_NODE_937, BOOST_PP_NODE_939)
#                                     define BOOST_PP_NODE_937(p) BOOST_PP_IIF(p(937), 937, 938)
#                                     define BOOST_PP_NODE_939(p) BOOST_PP_IIF(p(939), 939, 940)
#                                 define BOOST_PP_NODE_942(p) BOOST_PP_IIF(p(942), BOOST_PP_NODE_941, BOOST_PP_NODE_943)
#                                     define BOOST_PP_NODE_941(p) BOOST_PP_IIF(p(941), 941, 942)
#                                     define BOOST_PP_NODE_943(p) BOOST_PP_IIF(p(943), 943, 944)
#                         define BOOST_PP_NODE_952(p) BOOST_PP_IIF(p(952), BOOST_PP_NODE_948, BOOST_PP_NODE_956)
#                             define BOOST_PP_NODE_948(p) BOOST_PP_IIF(p(948), BOOST_PP_NODE_946, BOOST_PP_NODE_950)
#                                 define BOOST_PP_NODE_946(p) BOOST_PP_IIF(p(946), BOOST_PP_NODE_945, BOOST_PP_NODE_947)
#                                     define BOOST_PP_NODE_945(p) BOOST_PP_IIF(p(945), 945, 946)
#                                     define BOOST_PP_NODE_947(p) BOOST_PP_IIF(p(947), 947, 948)
#                                 define BOOST_PP_NODE_950(p) BOOST_PP_IIF(p(950), BOOST_PP_NODE_949, BOOST_PP_NODE_951)
#                                     define BOOST_PP_NODE_949(p) BOOST_PP_IIF(p(949), 949, 950)
#                                     define BOOST_PP_NODE_951(p) BOOST_PP_IIF(p(951), 951, 952)
#                             define BOOST_PP_NODE_956(p) BOOST_PP_IIF(p(956), BOOST_PP_NODE_954, BOOST_PP_NODE_958)
#                                 define BOOST_PP_NODE_954(p) BOOST_PP_IIF(p(954), BOOST_PP_NODE_953, BOOST_PP_NODE_955)
#                                     define BOOST_PP_NODE_953(p) BOOST_PP_IIF(p(953), 953, 954)
#                                     define BOOST_PP_NODE_955(p) BOOST_PP_IIF(p(955), 955, 956)
#                                 define BOOST_PP_NODE_958(p) BOOST_PP_IIF(p(958), BOOST_PP_NODE_957, BOOST_PP_NODE_959)
#                                     define BOOST_PP_NODE_957(p) BOOST_PP_IIF(p(957), 957, 958)
#                                     define BOOST_PP_NODE_959(p) BOOST_PP_IIF(p(959), 959, 960)
#                 define BOOST_PP_NODE_992(p) BOOST_PP_IIF(p(992), BOOST_PP_NODE_976, BOOST_PP_NODE_1008)
#                     define BOOST_PP_NODE_976(p) BOOST_PP_IIF(p(976), BOOST_PP_NODE_968, BOOST_PP_NODE_984)
#                         define BOOST_PP_NODE_968(p) BOOST_PP_IIF(p(968), BOOST_PP_NODE_964, BOOST_PP_NODE_972)
#                             define BOOST_PP_NODE_964(p) BOOST_PP_IIF(p(964), BOOST_PP_NODE_962, BOOST_PP_NODE_966)
#                                 define BOOST_PP_NODE_962(p) BOOST_PP_IIF(p(962), BOOST_PP_NODE_961, BOOST_PP_NODE_963)
#                                     define BOOST_PP_NODE_961(p) BOOST_PP_IIF(p(961), 961, 962)
#                                     define BOOST_PP_NODE_963(p) BOOST_PP_IIF(p(963), 963, 964)
#                                 define BOOST_PP_NODE_966(p) BOOST_PP_IIF(p(966), BOOST_PP_NODE_965, BOOST_PP_NODE_967)
#                                     define BOOST_PP_NODE_965(p) BOOST_PP_IIF(p(965), 965, 966)
#                                     define BOOST_PP_NODE_967(p) BOOST_PP_IIF(p(967), 967, 968)
#                             define BOOST_PP_NODE_972(p) BOOST_PP_IIF(p(972), BOOST_PP_NODE_970, BOOST_PP_NODE_974)
#                                 define BOOST_PP_NODE_970(p) BOOST_PP_IIF(p(970), BOOST_PP_NODE_969, BOOST_PP_NODE_971)
#                                     define BOOST_PP_NODE_969(p) BOOST_PP_IIF(p(969), 969, 970)
#                                     define BOOST_PP_NODE_971(p) BOOST_PP_IIF(p(971), 971, 972)
#                                 define BOOST_PP_NODE_974(p) BOOST_PP_IIF(p(974), BOOST_PP_NODE_973, BOOST_PP_NODE_975)
#                                     define BOOST_PP_NODE_973(p) BOOST_PP_IIF(p(973), 973, 974)
#                                     define BOOST_PP_NODE_975(p) BOOST_PP_IIF(p(975), 975, 976)
#                         define BOOST_PP_NODE_984(p) BOOST_PP_IIF(p(984), BOOST_PP_NODE_980, BOOST_PP_NODE_988)
#                             define BOOST_PP_NODE_980(p) BOOST_PP_IIF(p(980), BOOST_PP_NODE_978, BOOST_PP_NODE_982)
#                                 define BOOST_PP_NODE_978(p) BOOST_PP_IIF(p(978), BOOST_PP_NODE_977, BOOST_PP_NODE_979)
#                                     define BOOST_PP_NODE_977(p) BOOST_PP_IIF(p(977), 977, 978)
#                                     define BOOST_PP_NODE_979(p) BOOST_PP_IIF(p(979), 979, 980)
#                                 define BOOST_PP_NODE_982(p) BOOST_PP_IIF(p(982), BOOST_PP_NODE_981, BOOST_PP_NODE_983)
#                                     define BOOST_PP_NODE_981(p) BOOST_PP_IIF(p(981), 981, 982)
#                                     define BOOST_PP_NODE_983(p) BOOST_PP_IIF(p(983), 983, 984)
#                             define BOOST_PP_NODE_988(p) BOOST_PP_IIF(p(988), BOOST_PP_NODE_986, BOOST_PP_NODE_990)
#                                 define BOOST_PP_NODE_986(p) BOOST_PP_IIF(p(986), BOOST_PP_NODE_985, BOOST_PP_NODE_987)
#                                     define BOOST_PP_NODE_985(p) BOOST_PP_IIF(p(985), 985, 986)
#                                     define BOOST_PP_NODE_987(p) BOOST_PP_IIF(p(987), 987, 988)
#                                 define BOOST_PP_NODE_990(p) BOOST_PP_IIF(p(990), BOOST_PP_NODE_989, BOOST_PP_NODE_991)
#                                     define BOOST_PP_NODE_989(p) BOOST_PP_IIF(p(989), 989, 990)
#                                     define BOOST_PP_NODE_991(p) BOOST_PP_IIF(p(991), 991, 992)
#                     define BOOST_PP_NODE_1008(p) BOOST_PP_IIF(p(1008), BOOST_PP_NODE_1000, BOOST_PP_NODE_1016)
#                         define BOOST_PP_NODE_1000(p) BOOST_PP_IIF(p(1000), BOOST_PP_NODE_996, BOOST_PP_NODE_1004)
#                             define BOOST_PP_NODE_996(p) BOOST_PP_IIF(p(996), BOOST_PP_NODE_994, BOOST_PP_NODE_998)
#                                 define BOOST_PP_NODE_994(p) BOOST_PP_IIF(p(994), BOOST_PP_NODE_993, BOOST_PP_NODE_995)
#                                     define BOOST_PP_NODE_993(p) BOOST_PP_IIF(p(993), 993, 994)
#                                     define BOOST_PP_NODE_995(p) BOOST_PP_IIF(p(995), 995, 996)
#                                 define BOOST_PP_NODE_998(p) BOOST_PP_IIF(p(998), BOOST_PP_NODE_997, BOOST_PP_NODE_999)
#                                     define BOOST_PP_NODE_997(p) BOOST_PP_IIF(p(997), 997, 998)
#                                     define BOOST_PP_NODE_999(p) BOOST_PP_IIF(p(999), 999, 1000)
#                             define BOOST_PP_NODE_1004(p) BOOST_PP_IIF(p(1004), BOOST_PP_NODE_1002, BOOST_PP_NODE_1006)
#                                 define BOOST_PP_NODE_1002(p) BOOST_PP_IIF(p(1002), BOOST_PP_NODE_1001, BOOST_PP_NODE_1003)
#                                     define BOOST_PP_NODE_1001(p) BOOST_PP_IIF(p(1001), 1001, 1002)
#                                     define BOOST_PP_NODE_1003(p) BOOST_PP_IIF(p(1003), 1003, 1004)
#                                 define BOOST_PP_NODE_1006(p) BOOST_PP_IIF(p(1006), BOOST_PP_NODE_1005, BOOST_PP_NODE_1007)
#                                     define BOOST_PP_NODE_1005(p) BOOST_PP_IIF(p(1005), 1005, 1006)
#                                     define BOOST_PP_NODE_1007(p) BOOST_PP_IIF(p(1007), 1007, 1008)
#                         define BOOST_PP_NODE_1016(p) BOOST_PP_IIF(p(1016), BOOST_PP_NODE_1012, BOOST_PP_NODE_1020)
#                             define BOOST_PP_NODE_1012(p) BOOST_PP_IIF(p(1012), BOOST_PP_NODE_1010, BOOST_PP_NODE_1014)
#                                 define BOOST_PP_NODE_1010(p) BOOST_PP_IIF(p(1010), BOOST_PP_NODE_1009, BOOST_PP_NODE_1011)
#                                     define BOOST_PP_NODE_1009(p) BOOST_PP_IIF(p(1009), 1009, 1010)
#                                     define BOOST_PP_NODE_1011(p) BOOST_PP_IIF(p(1011), 1011, 1012)
#                                 define BOOST_PP_NODE_1014(p) BOOST_PP_IIF(p(1014), BOOST_PP_NODE_1013, BOOST_PP_NODE_1015)
#                                     define BOOST_PP_NODE_1013(p) BOOST_PP_IIF(p(1013), 1013, 1014)
#                                     define BOOST_PP_NODE_1015(p) BOOST_PP_IIF(p(1015), 1015, 1016)
#                             define BOOST_PP_NODE_1020(p) BOOST_PP_IIF(p(1020), BOOST_PP_NODE_1018, BOOST_PP_NODE_1022)
#                                 define BOOST_PP_NODE_1018(p) BOOST_PP_IIF(p(1018), BOOST_PP_NODE_1017, BOOST_PP_NODE_1019)
#                                     define BOOST_PP_NODE_1017(p) BOOST_PP_IIF(p(1017), 1017, 1018)
#                                     define BOOST_PP_NODE_1019(p) BOOST_PP_IIF(p(1019), 1019, 1020)
#                                 define BOOST_PP_NODE_1022(p) BOOST_PP_IIF(p(1022), BOOST_PP_NODE_1021, BOOST_PP_NODE_1023)
#                                     define BOOST_PP_NODE_1021(p) BOOST_PP_IIF(p(1021), 1021, 1022)
#                                     define BOOST_PP_NODE_1023(p) BOOST_PP_IIF(p(1023), 1023, 1024)
#
# endif
