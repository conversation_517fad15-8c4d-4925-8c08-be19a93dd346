import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Platform,
  ToastAndroid,
  Alert,
  Keyboard,
} from 'react-native';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {useSelector} from 'react-redux';
import {useNavigation} from '@react-navigation/native';
import {SafeAreaView, SafeAreaProvider} from 'react-native-safe-area-context';

// Import screens
import HomeScreen from '../screens/HomeScreen';
import ShopScreen from '../screens/ShopScreen';
import ProfileScreen from '../screens/ProfileScreen';
import LoginScreen from '../screens/LoginScreen';
import CartScreen from '../screens/CartScreen';

// Create tab navigator
const Tab = createBottomTabNavigator();

// Get screen dimensions
const {width} = Dimensions.get('window');

// Custom tab bar component
const CustomTabBar = ({state, descriptors, navigation}) => {
  // Get cart items count from Redux store
  const cart = useSelector(state => state.order.cart);
  const isLoggedIn = useSelector(state => state.auth.isLoggedIn);
  const [isCartVisible, setIsCartVisible] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastType, setToastType] = useState('error');
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);

  // Handle keyboard visibility
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      () => setIsKeyboardVisible(true),
    );
    const keyboardDidHideListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      () => setIsKeyboardVisible(false),
    );

    return () => {
      keyboardDidShowListener?.remove();
      keyboardDidHideListener?.remove();
    };
  }, []);

  // Calculate total item count in cart
  const getTotalItemCount = () => {
    if (!cart?.items) return 0;
    return cart.items.reduce((total, item) => total + parseInt(item.dqty), 0);
  };

  // Handle cart modal close
  const handleCartClosed = isEmpty => {
    setIsCartVisible(false);
  };

  // Show toast message
  const showToastMessage = (message, type = 'error') => {
    setToastMessage(message);
    setToastType(type);
    setShowToast(true);
    setTimeout(() => setShowToast(false), 3000);

    // Also show native toast on Android for better UX
    if (Platform.OS === 'android') {
      ToastAndroid.show(message, ToastAndroid.SHORT);
    }
  };

  // Don't render tab bar when keyboard is visible on Android
  if (Platform.OS === 'android' && isKeyboardVisible) {
    return (
      <View>
        {/* Toast Message */}
        {showToast && (
          <View
            style={[
              styles.toast,
              toastType === 'success' ? styles.successToast : styles.errorToast,
            ]}>
            <Text style={styles.toastText}>{toastMessage}</Text>
          </View>
        )}

        {/* Cart Modal */}
        <CartScreen
          visible={isCartVisible}
          onClose={isEmpty => handleCartClosed(isEmpty)}
          onProceedToCheckout={() => navigation.navigate('Checkout')}
        />
      </View>
    );
  }

  return (
    <View style={{backgroundColor: '#FFFFFF'}}>
      {/* Toast Message */}
      {showToast && (
        <View
          style={[
            styles.toast,
            toastType === 'success' ? styles.successToast : styles.errorToast,
          ]}>
          <Text style={styles.toastText}>{toastMessage}</Text>
        </View>
      )}

      {/* Cart Modal */}
      <CartScreen
        visible={isCartVisible}
        onClose={isEmpty => handleCartClosed(isEmpty)}
        onProceedToCheckout={() => navigation.navigate('Checkout')}
      />

      {/* Tab Bar */}
      <SafeAreaView
        style={{backgroundColor: '#FFFFFF'}}
        edges={['right', 'left']} // Exclude bottom edge to reduce margin
      >
        <View style={styles.tabBar}>
          {state.routes.map((route, index) => {
            const {options} = descriptors[route.key];
            const label =
              options.tabBarLabel !== undefined
                ? options.tabBarLabel
                : options.title !== undefined
                ? options.title
                : route.name;

            const isFocused = state.index === index;

            // Don't render the Cart tab in the tab bar
            if (route.name === 'CartTab') {
              return null;
            }

            // Custom rendering for each tab
            let icon;
            let color = isFocused ? '#000000' : '#AAAAAA';

            switch (route.name) {
              case 'HomeTab':
                icon = <Icon name="home" size={24} color={color} />;
                break;
              case 'ShopTab':
                icon = <Icon name="restaurant-menu" size={24} color={color} />;
                break;
              case 'ProfileTab':
                icon = <Icon name="person" size={24} color={color} />;
                break;
              default:
                icon = null;
            }

            const onPress = () => {
              const event = navigation.emit({
                type: 'tabPress',
                target: route.key,
                canPreventDefault: true,
              });

              if (!isFocused && !event.defaultPrevented) {
                navigation.navigate(route.name);
              }
            };

            return (
              <TouchableOpacity
                key={index}
                accessibilityRole="button"
                accessibilityState={isFocused ? {selected: true} : {}}
                accessibilityLabel={options.tabBarAccessibilityLabel}
                testID={options.tabBarTestID}
                onPress={onPress}
                style={styles.tabItem}>
                {icon}
                <Text
                  style={isFocused ? styles.tabLabelActive : styles.tabLabel}>
                  {label}
                </Text>
              </TouchableOpacity>
            );
          })}

          {/* Cart Button */}
          <TouchableOpacity
            style={styles.tabItem}
            onPress={() => {
              // Check if cart is empty
              const isCartEmpty = !cart?.items || cart.items.length === 0;
              if (isCartEmpty) {
                // Show toast message if cart is empty
                showToastMessage('Cart is Empty', 'error');
              } else {
                // Open cart modal
                setIsCartVisible(true);
              }
            }}>
            <View style={styles.cartIconContainer}>
              <Icon name="shopping-cart" size={24} color="#AAAAAA" />
              {getTotalItemCount() > 0 && (
                <View style={styles.cartBadge}>
                  <Text style={styles.cartBadgeText}>
                    {getTotalItemCount()}
                  </Text>
                </View>
              )}
            </View>
            <Text style={styles.tabLabel}>Cart</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </View>
  );
};

// Main TabNavigator component
const TabNavigator = () => {
  // Get authentication state from Redux
  const isLoggedIn = useSelector(state => state.auth.isLoggedIn);

  // Profile tab component - conditionally render ProfileScreen or LoginScreen
  const ProfileTabComponent = () => {
    return isLoggedIn ? <ProfileScreen /> : <LoginScreen />;
  };

  return (
    <Tab.Navigator
      tabBar={props => <CustomTabBar {...props} />}
      screenOptions={{
        headerShown: false,
        // Keep all screens mounted and their state preserved
        lazy: false,
      }}
      initialRouteName="HomeTab" // Always start with HomeTab regardless of login status
    >
      <Tab.Screen
        name="HomeTab"
        component={HomeScreen}
        options={{
          tabBarLabel: 'Home',
        }}
      />
      <Tab.Screen
        name="ShopTab"
        component={ShopScreen}
        options={{
          tabBarLabel: 'Shop',
        }}
      />
      <Tab.Screen
        name="CartTab"
        component={View} // Placeholder component, not actually used
        options={{
          tabBarLabel: 'Cart',
        }}
      />
      <Tab.Screen
        name="ProfileTab"
        component={ProfileTabComponent}
        options={{
          tabBarLabel: 'Profile',
        }}
      />
    </Tab.Navigator>
  );
};

const styles = StyleSheet.create({
  tabBar: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
    height: 80,
    paddingBottom: 4,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: -2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  tabItem: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8, // Add bottom margin to tab content
  },
  tabLabel: {
    fontSize: 12,
    color: '#AAAAAA',
    marginTop: 2,
    fontFamily: 'PlusJakartaSans-Regular',
  },
  tabLabelActive: {
    fontSize: 12,
    color: '#000000',
    marginTop: 2,
    fontFamily: 'PlusJakartaSans-Medium',
  },
  cartIconContainer: {
    position: 'relative',
  },
  cartBadge: {
    position: 'absolute',
    top: -5,
    right: -8,
    backgroundColor: '#E4002B',
    borderRadius: 10,
    minWidth: 18,
    height: 18,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 3,
  },
  cartBadgeText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
    fontFamily: 'PlusJakartaSans-Bold',
  },
  toast: {
    position: 'absolute',
    bottom: 85,
    left: 20,
    right: 20,
    padding: 12,
    borderRadius: 80,
    alignItems: 'center',
    zIndex: 100,
  },
  successToast: {
    backgroundColor: 'rgba(0, 128, 0, 1)',
  },
  errorToast: {
    backgroundColor: 'rgba(255, 0, 0, 1)',
  },
  toastText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default TabNavigator;
