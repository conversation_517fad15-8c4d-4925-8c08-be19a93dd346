"use client";

import { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Image,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  StatusBar,
  Dimensions,
  ActivityIndicator,
} from "react-native";
import Icon from "react-native-vector-icons/MaterialIcons";
import { useNavigation } from "@react-navigation/native";
import { useTheme } from "../theme/ThemeProvider";
import { useDispatch, useSelector } from "react-redux";
import { loginStart, loginSuccess, loginFailure } from "../store/authSlice";
import { API_ENDPOINTS, BUSINESS_ID } from "../config";

const { width } = Dimensions.get("window");

// Country codes for phone numbers
const countryCodes = [
  { label: "🇺🇸 +1 (US)", value: "+1", regex: /^\d{10}$/ },
  { label: "🇬🇧 +44 (UK)", value: "+44", regex: /^\d{10}$/ },
  { label: "🇮🇳 +91 (IN)", value: "+91", regex: /^\d{10}$/ },
  { label: "🇦🇪 +971 (UAE)", value: "+971", regex: /^\d{9}$/ },
  { label: "🇵🇰 +92 (PK)", value: "+92", regex: /^\d{10}$/ },
];

const LoginScreen = () => {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const dispatch = useDispatch();
  const { loading, error, isLoggedIn } = useSelector((state) => state.auth);
  const { logo } = useSelector((state) => state.order);

  const [isLogin, setIsLogin] = useState(true);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [name, setName] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");
  const [toastType, setToastType] = useState("error"); // success or error

  // State for tracking invalid fields
  const [invalidFields, setInvalidFields] = useState({
    email: false,
    password: false,
    name: false,
    phone: false,
  });

  // Country code dropdown state
  // const [open, setOpen] = useState(false)
  // const [countryCode, setCountryCode] = useState("+92") // Default to Pakistan
  // const [items, setItems] = useState(countryCodes)
  const [isPhoneValid, setIsPhoneValid] = useState(true);

  // Set header options
  useEffect(() => {
    navigation.setOptions({
      headerShown: false,
    });
  }, [navigation]);

  // Check if user is already logged in
  useEffect(() => {
    if (isLoggedIn) {
      // Check if we're in the tab navigator
      const parentState = navigation.getParent()?.getState();
      const isInTabNavigator = parentState?.routeNames.includes("HomeTab");

      if (isInTabNavigator) {
        // If we're in the tab navigator, we don't need to navigate
        // The ProfileTabComponent will handle showing the ProfileScreen
      } else {
        // If we're not in the tab navigator, navigate to Profile
        // navigation.replace("Profile");
      }
    }
  }, [isLoggedIn, navigation]);

  // Reset toast when switching between login and signup
  useEffect(() => {
    // Hide any existing toast when switching between login and signup
    if (showToast) {
      setShowToast(false);
    }
  }, [isLogin]);

  // Show toast message
  const showToastMessage = (message, type = "error") => {
    setToastMessage(message);
    setToastType(type);
    setShowToast(true);
    setTimeout(() => setShowToast(false), 3000);
  };

  // Validate phone number based on selected country
  const validatePhoneNumber = (number) => {
    if (!number) return true; // Empty is considered valid (will be caught by required field check)

    // Check if phone number length is between 10 and 13 digits
    return number.length >= 10 && number.length <= 13;
  };

  // Clear invalid state when user types
  const handleEmailChange = (text) => {
    setEmail(text);
    if (invalidFields.email) {
      setInvalidFields({ ...invalidFields, email: false });
    }
  };

  const handlePasswordChange = (text) => {
    setPassword(text);
    if (invalidFields.password) {
      setInvalidFields({ ...invalidFields, password: false });
    }
  };

  const handleNameChange = (text) => {
    setName(text);
    if (invalidFields.name) {
      setInvalidFields({ ...invalidFields, name: false });
    }
  };

  const handlePhoneChange = (number) => {
    // Only allow digits in the phone number
    const digitsOnly = number.replace(/\D/g, "");
    setPhoneNumber(digitsOnly);
    setIsPhoneValid(validatePhoneNumber(digitsOnly));
    if (invalidFields.phone) {
      setInvalidFields({ ...invalidFields, phone: false });
    }
  };

  const validateLoginForm = () => {
    // Reset invalid fields
    const newInvalidFields = {
      email: false,
      password: false,
      name: false,
      phone: false,
    };

    let isValid = true;

    // Check if all fields are empty
    const isEmailEmpty = !email.trim();
    const isPasswordEmpty = !password.trim();

    if (isEmailEmpty && isPasswordEmpty) {
      // All fields are empty
      showToastMessage("Please fill all fields");
      newInvalidFields.email = true;
      newInvalidFields.password = true;
      isValid = false;
    } else {
      // Check individual fields
      if (isEmailEmpty) {
        showToastMessage("Please enter your email address");
        newInvalidFields.email = true;
        isValid = false;
      } else {
        // Basic email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
          showToastMessage("Please enter a valid email address");
          newInvalidFields.email = true;
          isValid = false;
        }
      }

      if (isPasswordEmpty && isValid) {
        // Only show password error if no email error is shown
        showToastMessage("Please enter your password");
        newInvalidFields.password = true;
        isValid = false;
      }
    }

    setInvalidFields(newInvalidFields);
    return isValid;
  };

  const validateSignupForm = () => {
    // Reset invalid fields
    const newInvalidFields = {
      email: false,
      password: false,
      name: false,
      phone: false,
    };

    let isValid = true;

    // Check if all fields are empty
    const isNameEmpty = !name.trim();
    const isEmailEmpty = !email.trim();
    const isPhoneEmpty = !phoneNumber.trim();
    const isPasswordEmpty = !password.trim();

    if (isNameEmpty && isEmailEmpty && isPhoneEmpty && isPasswordEmpty) {
      // All fields are empty
      showToastMessage("Please fill all fields");
      newInvalidFields.name = true;
      newInvalidFields.email = true;
      newInvalidFields.phone = true;
      newInvalidFields.password = true;
      isValid = false;
    } else {
      // Check individual fields one by one, showing only one error at a time
      if (isNameEmpty) {
        showToastMessage("Please enter your full name");
        newInvalidFields.name = true;
        isValid = false;
      } else if (isEmailEmpty) {
        showToastMessage("Please enter your email address");
        newInvalidFields.email = true;
        isValid = false;
      } else if (!isEmailEmpty) {
        // Basic email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
          showToastMessage("Please enter a valid email address");
          newInvalidFields.email = true;
          isValid = false;
        }
      }

      if (isValid) {
        if (isPhoneEmpty) {
          showToastMessage("Please enter your phone number");
          newInvalidFields.phone = true;
          isValid = false;
        } else if (
          !isPhoneValid ||
          phoneNumber.length < 10 ||
          phoneNumber.length > 13
        ) {
          showToastMessage("Phone number must be between 10-13 digits");
          newInvalidFields.phone = true;
          isValid = false;
        }
      }

      if (isValid) {
        if (isPasswordEmpty) {
          showToastMessage("Please enter your password");
          newInvalidFields.password = true;
          isValid = false;
        } else if (password.length < 6) {
          showToastMessage("Password must be at least 6 characters long");
          newInvalidFields.password = true;
          isValid = false;
        }
      }
    }

    setInvalidFields(newInvalidFields);
    return isValid;
  };

  const handleLogin = async () => {
    if (!validateLoginForm()) return;

    dispatch(loginStart());

    try {
      const response = await fetch(
        `${API_ENDPOINTS.CART}/${BUSINESS_ID}/user/login`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            email: email,
            password: password,
            source: "app",
          }),
        }
      );

      const data = await response.json();

      if (data.status === 200) {
        // Extract only the needed fields to ensure we're storing the right data
        const userData = {
          user_id: data.result.user_id,
          user_email: data.result.user_email,
          user_fullname: data.result.user_fullname,
          user_cphone: data.result.user_cphone,
          td_user_id: data.result.td_user_id,
          token: data.result.token,
          refresh_token: data.result.refresh_token,
          // Include other fields that might be needed
          user_gender: data.result.user_gender || "",
          user_address: data.result.user_address || "",
          user_fbid: data.result.user_fbid || "",
          app_secret: data.result.app_secret || "",
          user_dob: data.result.user_dob || "",
          user_city: data.result.user_city || "",
          payment_settings: data.result.payment_settings || null,
        };

        dispatch(loginSuccess(userData));
        showToastMessage("Login successful", "success");

        // Check if we're in the tab navigator
        const parentState = navigation.getParent()?.getState();
        const isInTabNavigator = parentState?.routeNames.includes("HomeTab");

        if (isInTabNavigator) {
          // If we're in the tab navigator, we don't need to navigate
          // The ProfileTabComponent will handle showing the ProfileScreen
        } else {
          // If we're not in the tab navigator, navigate to Profile
          // navigation.navigate("Profile");
        }
      } else {
        dispatch(loginFailure(data.message || "Login failed"));
        showToastMessage(data.message || "Login failed");
      }
    } catch (error) {
      dispatch(loginFailure(error.message || "Network error"));
      showToastMessage("Network error. Please try again.");
    }
  };

  // Update the handleSignup function similarly
  const handleSignup = async () => {
    if (!validateSignupForm()) return;

    dispatch(loginStart());

    try {
      // Create the payload according to the required structure
      const payload = {
        name: name,
        email: email,
        phone: phoneNumber,
        gender: "",
        password: password,
        dob: "",
        city: "",
        address: "",
        token: "",
        device: Platform.OS, // "ios" or "android"
        source: "app",
        facebook_id: "",
        google_id: "",
        apple_id: "",
      };

      const response = await fetch(API_ENDPOINTS.SIGNUP, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      const data = await response.json();

      if (data.status === 200) {
        // Extract only the needed fields to ensure we're storing the right data
        const userData = {
          user_id: data.result.user_id,
          user_email: data.result.user_email,
          user_fullname: data.result.user_fullname,
          user_cphone: data.result.user_cphone,
          td_user_id: data.result.td_user_id,
          token: data.result.token,
          refresh_token: data.result.refresh_token,
          // Include other fields that might be needed
          user_gender: data.result.user_gender || "",
          user_address: data.result.user_address || "",
          user_fbid: data.result.user_fbid || "",
          app_secret: data.result.app_secret || "",
          user_dob: data.result.user_dob || "",
          user_city: data.result.user_city || "",
          payment_settings: data.result.payment_settings || null,
        };

        dispatch(loginSuccess(userData));
        showToastMessage("Signup successful", "success");

        // Check if we're in the tab navigator
        const parentState = navigation.getParent()?.getState();
        const isInTabNavigator = parentState?.routeNames.includes("HomeTab");

        if (isInTabNavigator) {
          // If we're in the tab navigator, we don't need to navigate
          // The ProfileTabComponent will handle showing the ProfileScreen
        } else {
          // If we're not in the tab navigator, navigate to Profile
          // navigation.navigate("Profile");
        }
      } else {
        dispatch(loginFailure(data.message || "Signup failed"));
        showToastMessage(data.message || "Signup failed");
      }
    } catch (error) {
      dispatch(loginFailure(error.message || "Network error"));
      showToastMessage("Network error. Please try again.");
    }
  };

  const handleSubmit = () => {
    if (isLogin) {
      handleLogin();
    } else {
      handleSignup();
    }
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.container}
      >
        <ScrollView contentContainerStyle={styles.scrollContainer}>
          {/* Remove back button */}

          {/* Logo and Header */}
          <View style={styles.headerContainer}>
            {logo ? (
              <Image
                source={{ uri: logo }}
                style={styles.logo}
                resizeMode="contain"
              />
            ) : (
              <Image
                source={require("../assets/splash.jpg")}
                style={styles.logo}
                resizeMode="contain"
              />
            )}
            <Text style={styles.headerTitle}>
              {isLogin ? "Welcome Back" : "Create Account"}
            </Text>
            <Text style={styles.headerSubtitle}>
              {isLogin
                ? "Sign in to continue to your account"
                : "Sign up to start ordering your favorite food"}
            </Text>
          </View>

          {/* Auth Toggle */}
          <View style={styles.toggleSwitchContainer}>
            <TouchableOpacity
              style={[
                styles.toggleButton,
                isLogin ? styles.activeToggle : styles.inactiveToggle,
              ]}
              onPress={() => {
                if (!isLogin) {
                  // Reset invalid fields when switching to Login
                  setInvalidFields({
                    email: false,
                    password: false,
                    name: false,
                    phone: false,
                  });
                  setIsLogin(true);
                }
              }}
            >
              <Text
                style={[
                  styles.toggleText,
                  isLogin ? styles.activeToggleText : styles.inactiveToggleText,
                ]}
              >
                Login
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.toggleButton,
                !isLogin ? styles.activeToggle : styles.inactiveToggle,
              ]}
              onPress={() => {
                if (isLogin) {
                  // Reset invalid fields when switching to Sign Up
                  setInvalidFields({
                    email: false,
                    password: false,
                    name: false,
                    phone: false,
                  });
                  setIsLogin(false);
                }
              }}
            >
              <Text
                style={[
                  styles.toggleText,
                  !isLogin
                    ? styles.activeToggleText
                    : styles.inactiveToggleText,
                ]}
              >
                Sign Up
              </Text>
            </TouchableOpacity>
          </View>

          {/* Form */}
          <View style={styles.formContainer}>
            {!isLogin && (
              <View
                style={[
                  styles.inputContainer,
                  invalidFields.name && styles.inputError,
                ]}
              >
                <Icon
                  name="person"
                  size={24}
                  color={invalidFields.name ? "#FF3B30" : "#777777"}
                  style={styles.inputIcon}
                />
                <TextInput
                  style={styles.input}
                  placeholder="Full Name"
                  value={name}
                  onChangeText={handleNameChange}
                  autoCapitalize="words"
                />
              </View>
            )}

            <View
              style={[
                styles.inputContainer,
                invalidFields.email && styles.inputError,
              ]}
            >
              <Icon
                name="email"
                size={24}
                color={invalidFields.email ? "#FF3B30" : "#777777"}
                style={styles.inputIcon}
              />
              <TextInput
                style={styles.input}
                placeholder="Email Address"
                value={email}
                onChangeText={handleEmailChange}
                keyboardType="email-address"
                autoCapitalize="none"
              />
            </View>

            {!isLogin && (
              <View
                style={[
                  styles.inputContainer,
                  invalidFields.phone && styles.inputError,
                ]}
              >
                <Icon
                  name="phone"
                  size={24}
                  color={invalidFields.phone ? "#FF3B30" : "#777777"}
                  style={styles.inputIcon}
                />
                <TextInput
                  style={styles.input}
                  placeholder="Phone Number"
                  value={phoneNumber}
                  onChangeText={handlePhoneChange}
                  keyboardType="phone-pad"
                  maxLength={13}
                />
              </View>
            )}
            {!isPhoneValid && !isLogin && phoneNumber.length > 0 && (
              <Text style={styles.errorText}>
                Phone number must be between 10-13 digits
              </Text>
            )}

            <View
              style={[
                styles.inputContainer,
                invalidFields.password && styles.inputError,
              ]}
            >
              <Icon
                name="lock"
                size={24}
                color={invalidFields.password ? "#FF3B30" : "#777777"}
                style={styles.inputIcon}
              />
              <TextInput
                style={styles.input}
                placeholder="Password"
                value={password}
                onChangeText={handlePasswordChange}
                secureTextEntry={!showPassword}
              />
              <TouchableOpacity
                style={styles.passwordToggle}
                onPress={() => setShowPassword(!showPassword)}
              >
                <Icon
                  name={showPassword ? "visibility" : "visibility-off"}
                  size={24}
                  color={invalidFields.password ? "#FF3B30" : "#777777"}
                />
              </TouchableOpacity>
            </View>

            {isLogin && (
              <TouchableOpacity
                style={styles.forgotPasswordContainer}
                onPress={() => navigation.navigate("ForgotPasswordScreen")}
              >
                <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
              </TouchableOpacity>
            )}

            <TouchableOpacity
              style={styles.submitButton}
              onPress={handleSubmit}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <Text style={styles.submitButtonText}>
                  {isLogin ? "Login" : "Sign Up"}
                </Text>
              )}
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Toast Message */}
      {showToast && (
        <View
          style={[
            styles.toast,
            toastType === "success" ? styles.successToast : styles.errorToast,
          ]}
        >
          <Text style={styles.toastText}>{toastMessage}</Text>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  container: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingBottom: 40,
  },
  inputError: {
    borderWidth: 1,
    borderColor: "#FF3B30",
  },
  backButton: {
    position: "absolute",
    top: 16,
    left: 16,
    zIndex: 10,
  },
  headerContainer: {
    alignItems: "center",
    marginTop: 60,
    marginBottom: 30,
  },
  logo: {
    width: 120,
    height: 120,
    marginBottom: 24,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: "bold",
    marginBottom: 8,
    color: "#000000",
  },
  headerSubtitle: {
    fontSize: 16,
    color: "#666666",
    textAlign: "center",
  },
  toggleSwitchContainer: {
    flexDirection: "row",
    backgroundColor: "#F5F5F5",
    borderRadius: 8,
    marginBottom: 24,
    overflow: "hidden",
  },
  toggleButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: "center",
  },
  activeToggle: {
    backgroundColor: "#000000",
  },
  inactiveToggle: {
    backgroundColor: "transparent",
  },
  toggleText: {
    fontSize: 16,
    fontWeight: "600",
  },
  activeToggleText: {
    color: "#FFFFFF",
  },
  inactiveToggleText: {
    color: "#666666",
  },
  formContainer: {
    marginBottom: 24,
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#F5F5F5",
    borderRadius: 8,
    marginBottom: 16,
    paddingHorizontal: 16,
    height: 56,
  },
  inputIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: "#000000",
  },
  passwordToggle: {
    padding: 8,
  },
  forgotPasswordContainer: {
    alignSelf: "flex-end",
    marginBottom: 24,
  },
  forgotPasswordText: {
    color: "#000000",
    fontSize: 14,
    fontWeight: "500",
  },
  submitButton: {
    backgroundColor: "#000000",
    borderRadius: 8,
    height: 56,
    justifyContent: "center",
    alignItems: "center",
  },
  submitButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "bold",
  },
  toast: {
    position: "absolute",
    bottom: 20,
    left: 20,
    right: 20,
    padding: 12,
    borderRadius: 80,
    alignItems: "center",
  },
  successToast: {
    backgroundColor: "rgba(0, 128, 0, 1)",
  },
  errorToast: {
    backgroundColor: "rgba(255, 0, 0, 1)",
  },
  toastText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "500",
  },
  errorText: {
    color: "#FF3B30",
    fontSize: 14,
    marginBottom: 16,
    marginLeft: 4,
  },
});

export default LoginScreen;
