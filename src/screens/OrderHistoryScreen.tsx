"use client";
import { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  SafeAreaView,
  StatusBar,
} from "react-native";
import Icon from "react-native-vector-icons/MaterialIcons";
import { useNavigation } from "@react-navigation/native";
import { useSelector } from "react-redux";
import { useTheme } from "../theme/ThemeProvider";
import { API_ENDPOINTS, BUSINESS_ID } from "../config";
import { formatPrice } from "../utils/cartUtils";

const OrderHistoryScreen = () => {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const [orders, setOrders] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [noOrders, setNoOrders] = useState(false);

  // Get user data from Redux store
  const { userData, isLoggedIn } = useSelector((state) => state.auth);
  // Get currency from Redux store
  const currency = useSelector((state) => state.order.currency);

  // Set header options
  useEffect(() => {
    navigation.setOptions({
      headerShown: true,
      headerTitle: "Order History",
      headerStyle: {
        backgroundColor: "#FFFFFF",
        elevation: 0,
        shadowOpacity: 0,
      },
      headerTintColor: "#000000",
      headerLeft: () => (
        <TouchableOpacity
          style={{ marginLeft: 16 }}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#000000" />
        </TouchableOpacity>
      ),
    });
  }, [navigation]);

  // Check if user is logged in
  useEffect(() => {
    if (!isLoggedIn) {
      navigation.replace("Login");
    }
  }, [isLoggedIn, navigation]);

  // Fetch orders from API
  useEffect(() => {
    if (isLoggedIn && userData?.user_email) {
      fetchOrders();
    }
  }, [isLoggedIn, userData]);

  const fetchOrders = async () => {
    setIsLoading(true);
    setError(null);
    setNoOrders(false);

    try {
      // Create form data for the request
      const formData = new FormData();
      formData.append("user_email", userData.user_email);
      formData.append("eatout_id", BUSINESS_ID);

      const response = await fetch(API_ENDPOINTS.ORDER_GET, {
        method: "POST",
        body: formData,
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      if (!response.ok) {
        throw new Error(`Server responded with status: ${response.status}`);
      }

      const data = await response.json();

      if (data.status === "1") {
        // Sort orders by date (newest first)
        const sortedOrders = data.result
          ? [...data.result].sort((a, b) => new Date(b.date) - new Date(a.date))
          : [];
        setOrders(sortedOrders);
      } else {
        // Instead of showing error, just set empty orders and noOrders flag
        setOrders([]);
        setNoOrders(true);
      }
    } catch (error) {
      console.error("Error fetching orders:", error);
      setError("Failed to load orders. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";

    try {
      const date = new Date(dateString);
      return date.toLocaleDateString("en-US", {
        day: "numeric",
        month: "short",
        year: "numeric",
      });
    } catch (e) {
      return dateString;
    }
  };

  // Format time
  const formatTime = (dateString) => {
    if (!dateString) return "N/A";

    try {
      const date = new Date(dateString);
      let hours = date.getHours();
      const minutes = date.getMinutes();
      const ampm = hours >= 12 ? "PM" : "AM";
      hours = hours % 12;
      hours = hours ? hours : 12; // the hour '0' should be '12'
      const formattedMinutes = minutes < 10 ? "0" + minutes : minutes;
      return `${hours}:${formattedMinutes} ${ampm}`;
    } catch (e) {
      return "";
    }
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case "Delivered":
        return "#4CAF50";
      case "Confirmed":
        return "#4CAF50"; // Green for confirmed
      case "Cancelled":
        return "#FF3B30";
      case "Pending":
        return "#FF9800"; // Orange for pending
      default:
        return "#000000";
    }
  };

  // Render order item
  const renderOrderItem = ({ item }) => (
    <View style={styles.orderCard}>
      <View style={styles.orderHeader}>
        <Text style={styles.orderId}>#{item.order_id}</Text>
        <View
          style={[
            styles.statusBadge,
            { backgroundColor: getStatusColor(item.status) },
          ]}
        >
          <Text style={styles.statusText}>{item.status}</Text>
        </View>
      </View>

      <View style={styles.orderNamePriceRow}>
        <Text style={styles.orderName}>{item.name || "Test Order"}</Text>
        <Text style={styles.priceText}>
          {formatPrice(item.grand_total, currency || "Rs")}
        </Text>
      </View>

      <View style={styles.locationContainer}>
        <Icon name="location-on" size={20} color="#000000" />
        <Text style={styles.locationText}>
          {item.user_area || "Model Town"}
        </Text>
      </View>

      <View style={styles.dateContainer}>
        <Icon name="access-time" size={20} color="#000000" />
        <Text style={styles.dateText}>{formatDate(item.date)}</Text>
        <Text style={styles.timeText}>{formatTime(item.date)}</Text>
      </View>

      <TouchableOpacity
        style={styles.viewDetailButton}
        onPress={() =>
          navigation.navigate("OrderDetail", { orderId: item.order_id })
        }
      >
        <Text style={styles.viewDetailText}>VIEW ORDER DETAIL</Text>
      </TouchableOpacity>
    </View>
  );

  // Render empty state
  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Icon name="receipt-long" size={64} color="#CCCCCC" />
      <Text style={styles.emptyTitle}>No Orders Found</Text>
      <Text style={styles.emptyText}>You haven't placed any orders yet.</Text>
      <TouchableOpacity
        style={styles.browseButton}
        onPress={() => navigation.goBack()}
      >
        <Text style={styles.browseButtonText}>Browse Menu</Text>
      </TouchableOpacity>
    </View>
  );

  // Render no orders state (when API returns status not equal to "1")
  const renderNoOrdersState = () => (
    <View style={styles.emptyContainer}>
      <Icon name="restaurant" size={64} color="#CCCCCC" />
      <Text style={styles.emptyTitle}>No Orders... Yet!</Text>
      <Text style={styles.emptyText}>
        Your future meals are waiting to be added to this list.
      </Text>
      <TouchableOpacity
        style={styles.browseButton}
        onPress={() => navigation.goBack()}
      >
        <Text style={styles.browseButtonText}>Browse Menu</Text>
      </TouchableOpacity>
    </View>
  );

  // Render error state
  const renderErrorState = () => (
    <View style={styles.emptyContainer}>
      <Icon name="error-outline" size={64} color="#FF3B30" />
      <Text style={styles.emptyTitle}>Something Went Wrong</Text>
      <Text style={styles.emptyText}>{error}</Text>
      <TouchableOpacity style={styles.browseButton} onPress={fetchOrders}>
        <Text style={styles.browseButtonText}>Try Again</Text>
      </TouchableOpacity>
    </View>
  );

  if (isLoading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#000000" />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      <View style={styles.container}>
        {error ? (
          renderErrorState()
        ) : noOrders ? (
          renderNoOrdersState()
        ) : (
          <FlatList
            data={orders}
            renderItem={renderOrderItem}
            keyExtractor={(item) => item.order_id}
            contentContainerStyle={styles.ordersList}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={renderEmptyState}
            ItemSeparatorComponent={() => <View style={styles.separator} />}
          />
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
  },
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  ordersList: {
    paddingBottom: 24,
  },
  orderCard: {
    backgroundColor: "#FFFFFF",
    padding: 16,
  },
  separator: {
    height: 1,
    backgroundColor: "#F0F0F0",
    marginHorizontal: 16,
  },
  orderHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  orderId: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#000000",
  },
  statusBadge: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  statusText: {
    fontSize: 14,
    color: "#FFFFFF",
    fontWeight: "600",
  },
  orderNamePriceRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
    paddingHorizontal: 0,
  },
  orderName: {
    fontSize: 18,
    fontWeight: "500",
    color: "#000000",
  },
  priceText: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#000000",
  },
  locationContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  locationText: {
    fontSize: 16,
    color: "#000000",
    marginLeft: 8,
  },
  dateContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  dateText: {
    fontSize: 16,
    color: "#000000",
    marginLeft: 8,
    flex: 1,
  },
  timeText: {
    fontSize: 16,
    color: "#000000",
    fontWeight: "500",
  },
  viewDetailButton: {
    backgroundColor: "#000000",
    borderRadius: 30,
    paddingVertical: 14,
    alignItems: "center",
    justifyContent: "center",
  },
  viewDetailText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#000000",
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    color: "#666666",
    marginBottom: 24,
    textAlign: "center",
  },
  browseButton: {
    backgroundColor: "#000000",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  browseButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "500",
  },
});

export default OrderHistoryScreen;
