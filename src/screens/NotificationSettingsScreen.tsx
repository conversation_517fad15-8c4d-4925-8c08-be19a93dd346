"use client";
import { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  ActivityIndicator,
  SafeAreaView,
  StatusBar,
  Alert,
  Clipboard,
  ToastAndroid,
  Platform,
} from "react-native";
import Icon from "react-native-vector-icons/MaterialIcons";
import { useNavigation } from "@react-navigation/native";
import { useSelector } from "react-redux";
import { useTheme } from "../theme/ThemeProvider";
import {
  getNotificationSettings,
  saveNotificationSettings,
  defaultNotificationSettings,
  sendTestNotification,
  getStoredPushToken,
} from "../services/NotificationService";

const NotificationSettingsScreen = () => {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [settings, setSettings] = useState(defaultNotificationSettings);
  const [pushToken, setPushToken] = useState("");

  // Get user data from Redux store
  const { userData, isLoggedIn } = useSelector((state) => state.auth);

  // Check if user is logged in
  useEffect(() => {
    if (!isLoggedIn) {
      navigation.replace("Login");
    } else {
      loadSettings();
      loadPushToken();
    }
  }, [isLoggedIn, navigation]);

  // Load push token
  const loadPushToken = async () => {
    try {
      const token = await getStoredPushToken();
      setPushToken(token || "No token available");
    } catch (error) {
      console.error("Error loading push token:", error);
      setPushToken("Error loading token");
    }
  };

  // Add a function to copy the token to clipboard
  const copyTokenToClipboard = () => {
    if (
      pushToken &&
      pushToken !== "No token available" &&
      pushToken !== "Error loading token"
    ) {
      Clipboard.setString(pushToken);

      console.log("Token copied to clipboard:", pushToken);

      // Show toast or alert based on platform
      if (Platform.OS === "android") {
        ToastAndroid.show("Token copied to clipboard", ToastAndroid.SHORT);
      } else {
        Alert.alert("Copied", "Token copied to clipboard");
      }
    }
  };

  // Load notification settings
  const loadSettings = async () => {
    setIsLoading(true);
    try {
      const savedSettings = await getNotificationSettings();
      setSettings(savedSettings);
    } catch (error) {
      console.error("Error loading notification settings:", error);
      Alert.alert("Error", "Failed to load notification settings");
    } finally {
      setIsLoading(false);
    }
  };

  // Handle toggle changes
  const handleToggle = (key) => {
    setSettings((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  // Save settings to backend and local storage
  const handleSave = async () => {
    setIsSaving(true);
    try {
      // Save to local storage only
      await saveNotificationSettings(settings);

      // Show success message
      Alert.alert("Success", "Notification settings updated successfully");
    } catch (error) {
      console.error("Error saving notification settings:", error);
      Alert.alert("Error", "Failed to save notification settings");
    } finally {
      setIsSaving(false);
    }
  };

  // Send test notification
  const sendTestOrderNotification = async () => {
    if (!settings.orderUpdates) {
      Alert.alert(
        "Notifications Disabled",
        "Please enable Order Updates notifications first"
      );
      return;
    }

    await sendTestNotification(
      "Order Update",
      "Your order #12345 has been confirmed and is being prepared!",
      {
        type: "order_update",
        orderId: "12345",
      }
    );
  };

  const sendTestPromotionNotification = async () => {
    if (!settings.promotions) {
      Alert.alert(
        "Notifications Disabled",
        "Please enable Promotions notifications first"
      );
      return;
    }

    await sendTestNotification(
      "Special Offer",
      "Get 20% off on your next order with code SPECIAL20",
      {
        type: "promotion",
      }
    );
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#000000" />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        <View style={styles.headerContainer}>
          <Text style={styles.headerTitle}>Notification Preferences</Text>
          <Text style={styles.headerSubtitle}>
            Manage what types of notifications you receive
          </Text>
        </View>

        <View style={styles.settingsContainer}>
          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Icon
                name="notifications-active"
                size={24}
                color="#000000"
              />
              <View style={styles.settingTextContainer}>
                <Text style={styles.settingTitle}>Order Updates</Text>
                <Text style={styles.settingDescription}>
                  Receive notifications about your order status
                </Text>
              </View>
            </View>
            <Switch
              value={settings.orderUpdates}
              onValueChange={() => handleToggle("orderUpdates")}
              trackColor={{ false: "#D1D1D1", true: "#000000" }}
              thumbColor="#FFFFFF"
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Icon name="local-offer" size={24} color="#000000" />
              <View style={styles.settingTextContainer}>
                <Text style={styles.settingTitle}>Promotions & Offers</Text>
                <Text style={styles.settingDescription}>
                  Receive notifications about special deals and promotions
                </Text>
              </View>
            </View>
            <Switch
              value={settings.promotions}
              onValueChange={() => handleToggle("promotions")}
              trackColor={{ false: "#D1D1D1", true: "#000000" }}
              thumbColor="#FFFFFF"
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Icon name="campaign" size={24} color="#000000" />
              <View style={styles.settingTextContainer}>
                <Text style={styles.settingTitle}>General Announcements</Text>
                <Text style={styles.settingDescription}>
                  Receive notifications about app updates and news
                </Text>
              </View>
            </View>
            <Switch
              value={settings.general}
              onValueChange={() => handleToggle("general")}
              trackColor={{ false: "#D1D1D1", true: "#000000" }}
              thumbColor="#FFFFFF"
            />
          </View>
        </View>

        <TouchableOpacity
          style={styles.saveButton}
          onPress={handleSave}
          disabled={isSaving}
        >
          {isSaving ? (
            <ActivityIndicator size="small" color="#FFFFFF" />
          ) : (
            <Text style={styles.saveButtonText}>Save Settings</Text>
          )}
        </TouchableOpacity>

        <View style={styles.testContainer}>
          <Text style={styles.testTitle}>Test Notifications</Text>
          <Text style={styles.testDescription}>
            Send yourself a test notification to verify your settings
          </Text>

          <View style={styles.testButtonsContainer}>
            <TouchableOpacity
              style={[
                styles.testButton,
                !settings.orderUpdates && styles.disabledButton,
              ]}
              onPress={sendTestOrderNotification}
              disabled={!settings.orderUpdates}
            >
              <Icon name="receipt" size={20} color="#FFFFFF" />
              <Text style={styles.testButtonText}>Test Order Update</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.testButton,
                !settings.promotions && styles.disabledButton,
              ]}
              onPress={sendTestPromotionNotification}
              disabled={!settings.promotions}
            >
              <Icon name="local-offer" size={20} color="#FFFFFF" />
              <Text style={styles.testButtonText}>Test Promotion</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Push Token Section */}
        <View style={styles.tokenContainer}>
          <Text style={styles.tokenTitle}>Push Notification Token</Text>
          <Text style={styles.tokenDescription}>
            Your device token for push notifications
          </Text>

          <View style={styles.tokenBox}>
            <Text
              style={styles.tokenText}
              numberOfLines={3}
              ellipsizeMode="middle"
            >
              {pushToken}
            </Text>
          </View>

          <TouchableOpacity
            style={styles.copyButton}
            onPress={copyTokenToClipboard}
            disabled={
              !pushToken ||
              pushToken === "No token available" ||
              pushToken === "Error loading token"
            }
          >
            <Icon name="content-copy" size={20} color="#FFFFFF" />
            <Text style={styles.copyButtonText}>Copy Token</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.infoContainer}>
          <Icon name="info-outline" size={20} color="#666666" />
          <Text style={styles.infoText}>
            You can also manage notification permissions in your device
            settings.
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

// Add new styles for the token section
const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
  },
  container: {
    flex: 1,
    padding: 16,
  },
  headerContainer: {
    marginBottom: 24,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#000000",
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: "#666666",
  },
  settingsContainer: {
    marginBottom: 24,
  },
  settingItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#F0F0F0",
  },
  settingInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    marginRight: 16,
  },
  settingTextContainer: {
    marginLeft: 16,
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#000000",
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    color: "#666666",
  },
  saveButton: {
    backgroundColor: "#000000",
    borderRadius: 8,
    paddingVertical: 14,
    alignItems: "center",
    marginBottom: 24,
  },
  saveButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "bold",
  },
  testContainer: {
    backgroundColor: "#F8F8F8",
    borderRadius: 8,
    padding: 16,
    marginBottom: 24,
  },
  testTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#000000",
    marginBottom: 8,
  },
  testDescription: {
    fontSize: 14,
    color: "#666666",
    marginBottom: 16,
  },
  testButtonsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  testButton: {
    backgroundColor: "#000000",
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 16,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    flex: 1,
    marginHorizontal: 4,
  },
  disabledButton: {
    backgroundColor: "#CCCCCC",
  },
  testButtonText: {
    color: "#FFFFFF",
    fontSize: 14,
    fontWeight: "500",
    marginLeft: 8,
  },
  infoContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#F5F5F5",
    borderRadius: 8,
    padding: 12,
    marginBottom: 24,
  },
  infoText: {
    fontSize: 14,
    color: "#666666",
    marginLeft: 8,
    flex: 1,
  },
  // New styles for token section
  tokenContainer: {
    backgroundColor: "#F8F8F8",
    borderRadius: 8,
    padding: 16,
    marginBottom: 24,
  },
  tokenTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#000000",
    marginBottom: 8,
  },
  tokenDescription: {
    fontSize: 14,
    color: "#666666",
    marginBottom: 16,
  },
  tokenBox: {
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#EEEEEE",
    padding: 12,
    marginBottom: 16,
  },
  tokenText: {
    fontSize: 14,
    color: "#333333",
    fontFamily: Platform.OS === "ios" ? "Menlo" : "monospace",
  },
  copyButton: {
    backgroundColor: "#000000",
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 16,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    alignSelf: "center",
  },
  copyButtonText: {
    color: "#FFFFFF",
    fontSize: 14,
    fontWeight: "500",
    marginLeft: 8,
  },
});

export default NotificationSettingsScreen;
