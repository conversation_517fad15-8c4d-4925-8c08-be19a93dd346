"use client";

import type React from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import Icon from "react-native-vector-icons/MaterialIcons";
import { useSelector } from "react-redux";
import { formatPrice } from "../utils/cartUtils";

interface OptionSetRendererProps {
  optionSet: any;
  selectedOptions: any;
  isExpanded: boolean;
  onToggle: (optionId: string) => void;
  onSelect: (optionId: string, itemId: string) => void;
  renderInnerOptionSets?: (optionItem: any) => React.ReactNode;
}

const OptionSetRenderer: React.FC<OptionSetRendererProps> = ({
  optionSet,
  selectedOptions,
  isExpanded,
  onToggle,
  onSelect,
  renderInnerOptionSets,
}) => {
  const { currency } = useSelector((state: any) => state.order);

  const isRequired = optionSet.min_quantity !== "0";
  const isMultiSelect = optionSet.quantity > 1 || optionSet.quantity === "0";

  return (
    <View style={styles.optionSet}>
      {/* Option Items */}
      <View style={styles.optionItems}>
        {optionSet.items.map((optionItem) => {
          const isSelected =
            selectedOptions[optionSet.id]?.items?.[optionItem.id];

          return (
            <View key={optionItem.id}>
              <TouchableOpacity
                style={styles.optionItem}
                onPress={() => onSelect(optionSet.id, optionItem.id)}
              >
                {/* Radio or Checkbox */}
                <View style={styles.optionSelectContainer}>
                  {isMultiSelect ? (
                    <View
                      style={[
                        styles.checkbox,
                        isSelected && styles.checkboxSelected,
                      ]}
                    >
                      {isSelected && (
                        <Icon name="check" size={12} color="#FFFFFF" />
                      )}
                    </View>
                  ) : (
                    <View
                      style={[styles.radio, isSelected && styles.radioSelected]}
                    >
                      {isSelected && <View style={styles.radioInner} />}
                    </View>
                  )}
                  <Text style={styles.optionItemText}>{optionItem.name}</Text>
                </View>

                {/* Option Price (if not 0) */}
                {Number(optionItem.price) > 0 && (
                  <Text style={styles.optionItemPrice}>
                    {formatPrice(optionItem.price, currency, 0)}
                  </Text>
                )}
              </TouchableOpacity>

              {/* Render inner option sets if this option is selected */}
              {isSelected &&
                renderInnerOptionSets &&
                renderInnerOptionSets(optionItem)}
            </View>
          );
        })}
      </View>
    </View>
  );
};

// Update the OptionSetRenderer styles to match the new full-screen design
const styles = StyleSheet.create({
  optionSet: {
    marginBottom: 12,
    borderRadius: 8,
    overflow: "hidden",
    backgroundColor: "#FFFFFF",
  },
  optionItems: {
    backgroundColor: "#FFFFFF",
  },
  optionItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 0,
    borderBottomWidth: 1,
    borderBottomColor: "#F5F5F5",
  },
  optionSelectContainer: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  radio: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: "#CCCCCC",
    marginRight: 12,
    justifyContent: "center",
    alignItems: "center",
    flexShrink: 0,
  },
  radioSelected: {
    borderColor: "#000000",
  },
  radioInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: "#000000",
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: "#CCCCCC",
    marginRight: 12,
    justifyContent: "center",
    alignItems: "center",
    flexShrink: 0,
  },
  checkboxSelected: {
    backgroundColor: "#000000",
    borderColor: "#000000",
  },
  optionItemText: {
    fontSize: 16,
    color: "#000000",
    flex: 1,
  },
  optionItemPrice: {
    fontSize: 16,
    color: "#000000",
    fontWeight: "bold",
    marginLeft: 8,
    flexShrink: 0,
  },
  innerOptionContainer: {
    paddingLeft: 32, // Indent inner options
  },
});

export default OptionSetRenderer;
