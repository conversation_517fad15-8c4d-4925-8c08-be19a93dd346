"use client";

import { useState, useEffect } from "react";
import { View, Text, StyleSheet, TouchableOpacity } from "react-native";
import Icon from "react-native-vector-icons/MaterialIcons";
import moment from "moment";
import OrderScheduleModal from "./OrderScheduleModal";
import { useGetPickupHours } from "../../hooks/useGetPickupHours";

interface PickupDetailsProps {
  branchId: string;
  businessId: string;
  branchDetails: any;
  onScheduleChange: (schedule: { date: string; time: string }) => void;
}

const PickupDetails = ({
  branchId,
  businessId,
  branchDetails,
  onScheduleChange,
}: PickupDetailsProps) => {
  const [showScheduleModal, setShowScheduleModal] = useState(false);
  const [schedule, setSchedule] = useState<{
    date: string;
    time: string;
  } | null>(null);
  const [pickupSlots, setPickupSlots] = useState<string[]>([]);

  // Use the pickup hours hook
  const { loading, getPickupHoursAPICall } = useGetPickupHours();

  // Fetch pickup slots when date changes
  useEffect(() => {
    if (schedule?.date) {
      fetchPickupSlots(schedule.date);
    } else {
      // Set default date to today if not set
      const today = moment().format("YYYY-MM-DD");
      setSchedule((prev) => ({ ...prev, date: today }));
      fetchPickupSlots(today);
    }
  }, [schedule?.date]);

  const fetchPickupSlots = async (date: string) => {
    const slots = await getPickupHoursAPICall({
      bid: branchId,
      date: date,
      type: "pickup",
    });

    setPickupSlots(slots);

    // Auto-select first slot if none selected
    if (slots.length > 0 && !schedule?.time) {
      setSchedule((prev) => ({ ...prev, time: slots[0] }));
      onScheduleChange({ date, time: slots[0] });
    }
  };

  const handleOpenScheduleModal = () => {
    setShowScheduleModal(true);
  };

  const handleCloseScheduleModal = () => {
    setShowScheduleModal(false);
  };

  const handleSaveSchedule = (newSchedule: { date: string; time: string }) => {
    setSchedule(newSchedule);
    onScheduleChange(newSchedule);
  };

  const formatSchedule = (schedule: { date: string; time: string } | null) => {
    if (!schedule || !schedule.date || !schedule.time) {
      return "No pickup time selected";
    }

    const date = moment(schedule.date).format("ddd, MMM D");
    return `${date} at ${schedule.time}`;
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.iconContainer}>
          <Icon name="store" size={24} color="#0066cc" />
          <Text style={styles.headerText}>Pickup Details</Text>
        </View>
      </View>

      {/* Branch Information */}
      <View style={styles.branchInfo}>
        <Text style={styles.branchName}>
          {branchDetails?.name || "Store Location"}
        </Text>
        <Text style={styles.branchAddress}>
          {[branchDetails?.address, branchDetails?.area, branchDetails?.city]
            .filter(Boolean)
            .join(", ")}
        </Text>
      </View>

      {/* Pickup Schedule */}
      <View style={styles.scheduleContainer}>
        <Text style={styles.sectionTitle}>Pickup Time*</Text>

        {schedule?.date && schedule?.time ? (
          <View style={styles.scheduleInfo}>
            <View style={styles.scheduleTextContainer}>
              <Icon name="access-time" size={20} color="#666" />
              <Text style={styles.scheduleText}>
                {formatSchedule(schedule)}
              </Text>
            </View>
            <TouchableOpacity onPress={handleOpenScheduleModal}>
              <Text style={styles.editText}>Edit</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <TouchableOpacity
            style={styles.selectButton}
            onPress={handleOpenScheduleModal}
          >
            <Text style={styles.selectButtonText}>Select Pickup Time</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Pickup Instructions */}
      <View style={styles.instructionsContainer}>
        <Icon name="info-outline" size={20} color="#666" />
        <Text style={styles.instructionsText}>
          Please arrive at the store at your selected time to pick up your
          order.
        </Text>
      </View>

      {/* Schedule Modal */}
      <OrderScheduleModal
        visible={showScheduleModal}
        onClose={handleCloseScheduleModal}
        onSave={handleSaveSchedule}
        initialSchedule={schedule}
        pickupSlots={pickupSlots}
        isLoading={loading}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#fff",
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: "#F0F0F0",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  header: {
    marginBottom: 16,
  },
  iconContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  headerText: {
    fontSize: 18,
    fontWeight: "bold",
    marginLeft: 8,
    color: "#333",
  },
  branchInfo: {
    marginBottom: 16,
    padding: 12,
    backgroundColor: "#f9f9f9",
    borderRadius: 8,
  },
  branchName: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  branchAddress: {
    fontSize: 14,
    color: "#666",
  },
  scheduleContainer: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 8,
    color: "#333",
  },
  scheduleInfo: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 12,
    backgroundColor: "#f0f7ff",
    borderRadius: 8,
  },
  scheduleTextContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  scheduleText: {
    marginLeft: 8,
    fontSize: 16,
    color: "#333",
  },
  editText: {
    color: "#0066cc",
    fontSize: 14,
    fontWeight: "500",
  },
  selectButton: {
    backgroundColor: "#0066cc",
    padding: 12,
    borderRadius: 8,
    alignItems: "center",
  },
  selectButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "500",
  },
  instructionsContainer: {
    flexDirection: "row",
    backgroundColor: "#f9f9f9",
    padding: 12,
    borderRadius: 8,
    alignItems: "flex-start",
  },
  instructionsText: {
    marginLeft: 8,
    fontSize: 14,
    color: "#666",
    flex: 1,
  },
});

export default PickupDetails;
