"use client";
import { View, Text, StyleSheet } from "react-native";
import Icon from "react-native-vector-icons/MaterialIcons";

interface OrderTypeDetailsProps {
  orderType: string;
  userDetails: any;
  setUserDetails: (details: any) => void;
}

const OrderTypeDetails = ({ orderType }: OrderTypeDetailsProps) => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>
        {orderType === "delivery" ? "Delivery Details" : "Pickup Details"}
      </Text>

      <View style={styles.orderTypeIndicator}>
        <Icon
          name={orderType === "delivery" ? "delivery-dining" : "store"}
          size={24}
          color="#0066cc"
        />
        <Text style={styles.orderTypeText}>
          {orderType === "delivery" ? "Delivery" : "Pickup"}
        </Text>
      </View>

      {orderType === "pickup" && (
        <View style={styles.pickupInfo}>
          <Icon name="info-outline" size={20} color="#666" />
          <Text style={styles.pickupInfoText}>
            Please arrive at the store to pick up your order when ready.
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#fff",
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 16,
    color: "#333",
  },
  orderTypeIndicator: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
    padding: 12,
    backgroundColor: "#f0f7ff",
    borderRadius: 8,
  },
  orderTypeText: {
    marginLeft: 8,
    fontSize: 16,
    color: "#0066cc",
    fontWeight: "500",
  },
  pickupInfo: {
    flexDirection: "row",
    backgroundColor: "#f9f9f9",
    padding: 12,
    borderRadius: 8,
    alignItems: "flex-start",
  },
  pickupInfoText: {
    marginLeft: 8,
    fontSize: 14,
    color: "#666",
    flex: 1,
  },
});

export default OrderTypeDetails;
